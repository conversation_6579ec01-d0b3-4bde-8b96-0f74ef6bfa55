=== 计算节点环境检查 ===
节点名称: esplhpc-cp079
Python路径: /home/<USER>/.conda/envs/promptmr/bin/python
GCC版本: gcc (GCC) 11.3.0
G++版本: g++ (GCC) 11.3.0
CUDA可用性: True
CUDA版本: 12.1
PyTorch版本: 2.4.1+cu121

=== CUDA工具链检查 ===
nvcc路径: /cm/shared/apps/cuda12.1/toolkit/12.1.1/bin/nvcc
nvcc版本:
nvcc: NVIDIA (R) Cuda compiler driver
Copyright (c) 2005-2023 NVIDIA Corporation
Built on Mon_Apr__3_17:16:06_PDT_2023
Cuda compilation tools, release 12.1, V12.1.105
Build cuda_12.1.r12.1/compiler.32688072_0
CUDA_HOME: /cm/shared/apps/cuda12.1/toolkit/12.1.1

=== 清理编译缓存 ===
=== 最终环境变量 ===
CUDA_HOME: /cm/shared/apps/cuda12.1/toolkit/12.1.1
TORCH_CUDA_ARCH_LIST: 8.9
CC: /cm/shared/apps/gcc11/11.3.0/bin/gcc
CXX: /cm/shared/apps/gcc11/11.3.0/bin/g++
=== 安装StyleGAN3依赖 ===
Requirement already satisfied: click in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (8.1.8)
Requirement already satisfied: requests in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (2.32.3)
Requirement already satisfied: tqdm in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (4.67.1)
Requirement already satisfied: pyspng in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (0.1.3)
Requirement already satisfied: ninja in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (1.13.0)
Requirement already satisfied: imageio-ffmpeg==0.4.3 in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (0.4.3)
Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (from requests) (3.3.2)
Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (from requests) (3.7)
Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (from requests) (2.2.3)
Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (from requests) (2024.8.30)
Requirement already satisfied: numpy in /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages (from pyspng) (1.24.3)
=== Step 1: T2map Data Preprocessing ===
预处理生成图像数量: 1011
=== Step 2: Creating StyleGAN3 Dataset ===
T2map dataset already exists
=== Step 3: StyleGAN3 T2map Training ===

Training options:
{
  "G_kwargs": {
    "class_name": "training.networks_stylegan3.Generator",
    "z_dim": 512,
    "w_dim": 512,
    "mapping_kwargs": {
      "num_layers": 2
    },
    "channel_base": 65536,
    "channel_max": 1024,
    "magnitude_ema_beta": 0.9994456359721023,
    "conv_kernel": 1,
    "use_radial_filters": true
  },
  "D_kwargs": {
    "class_name": "training.networks_stylegan2.Discriminator",
    "block_kwargs": {
      "freeze_layers": 0
    },
    "mapping_kwargs": {},
    "epilogue_kwargs": {
      "mbstd_group_size": 4
    },
    "channel_base": 32768,
    "channel_max": 512
  },
  "G_opt_kwargs": {
    "class_name": "torch.optim.Adam",
    "betas": [
      0,
      0.99
    ],
    "eps": 1e-08,
    "lr": 0.0025
  },
  "D_opt_kwargs": {
    "class_name": "torch.optim.Adam",
    "betas": [
      0,
      0.99
    ],
    "eps": 1e-08,
    "lr": 0.002
  },
  "loss_kwargs": {
    "class_name": "training.loss.StyleGAN2Loss",
    "r1_gamma": 6.6,
    "blur_init_sigma": 10,
    "blur_fade_kimg": 100.0
  },
  "data_loader_kwargs": {
    "pin_memory": true,
    "prefetch_factor": 2,
    "num_workers": 3
  },
  "training_set_kwargs": {
    "class_name": "training.dataset.ImageFolderDataset",
    "path": "../data/t2map_dataset.zip",
    "use_labels": false,
    "max_size": 1011,
    "xflip": true,
    "resolution": 512,
    "random_seed": 42
  },
  "num_gpus": 4,
  "batch_size": 16,
  "batch_gpu": 4,
  "metrics": [
    "fid50k_full"
  ],
  "total_kimg": 15000,
  "kimg_per_tick": 10,
  "image_snapshot_ticks": 100,
  "network_snapshot_ticks": 100,
  "random_seed": 42,
  "ema_kimg": 5.0,
  "augment_kwargs": {
    "class_name": "training.augment.AugmentPipe",
    "xflip": 1,
    "rotate90": 1,
    "xint": 1,
    "scale": 1,
    "rotate": 1,
    "aniso": 1,
    "xfrac": 1,
    "brightness": 1,
    "contrast": 1,
    "lumaflip": 1,
    "hue": 1,
    "saturation": 1
  },
  "ada_target": 0.6,
  "run_dir": "../outputs/t2map_models/00002-stylegan3-r-t2map_dataset-gpus4-batch16-gamma6.6"
}

Output directory:    ../outputs/t2map_models/00002-stylegan3-r-t2map_dataset-gpus4-batch16-gamma6.6
Number of GPUs:      4
Batch size:          16 images
Training duration:   15000 kimg
Dataset path:        ../data/t2map_dataset.zip
Dataset size:        1011 images
Dataset resolution:  512
Dataset labels:      False
Dataset x-flips:     True

Creating output directory...
Launching processes...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
Loading training set...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."

Num images:  2022
Image shape: [3, 512, 512]
Label shape: [0]

Constructing networks...
Setting up PyTorch plugin "filtered_lrelu_plugin"... /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Done.

Generator                     Parameters  Buffers  Output shape         Datatype
---                           ---         ---      ---                  ---     
mapping.fc0                   262656      -        [4, 512]             float32 
mapping.fc1                   262656      -        [4, 512]             float32 
mapping                       -           512      [4, 16, 512]         float32 
synthesis.input.affine        2052        -        [4, 4]               float32 
synthesis.input               1048576     3081     [4, 1024, 36, 36]    float32 
synthesis.L0_36_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L0_36_1024          1049600     157      [4, 1024, 36, 36]    float32 
synthesis.L1_36_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L1_36_1024          1049600     157      [4, 1024, 36, 36]    float32 
synthesis.L2_52_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L2_52_1024          1049600     169      [4, 1024, 52, 52]    float32 
synthesis.L3_52_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L3_52_1024          1049600     157      [4, 1024, 52, 52]    float32 
synthesis.L4_84_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L4_84_1024          1049600     169      [4, 1024, 84, 84]    float16 
synthesis.L5_84_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L5_84_1024          1049600     157      [4, 1024, 84, 84]    float16 
synthesis.L6_148_1024.affine  525312      -        [4, 1024]            float32 
synthesis.L6_148_1024         1049600     169      [4, 1024, 148, 148]  float16 
synthesis.L7_148_967.affine   525312      -        [4, 1024]            float32 
synthesis.L7_148_967          991175      157      [4, 967, 148, 148]   float16 
synthesis.L8_276_645.affine   496071      -        [4, 967]             float32 
synthesis.L8_276_645          624360      169      [4, 645, 276, 276]   float16 
synthesis.L9_276_431.affine   330885      -        [4, 645]             float32 
synthesis.L9_276_431          278426      157      [4, 431, 276, 276]   float16 
synthesis.L10_532_287.affine  221103      -        [4, 431]             float32 
synthesis.L10_532_287         123984      169      [4, 287, 532, 532]   float16 
synthesis.L11_532_192.affine  147231      -        [4, 287]             float32 
synthesis.L11_532_192         55296       157      [4, 192, 532, 532]   float16 
synthesis.L12_532_128.affine  98496       -        [4, 192]             float32 
synthesis.L12_532_128         24704       25       [4, 128, 532, 532]   float16 
synthesis.L13_512_128.affine  65664       -        [4, 128]             float32 
synthesis.L13_512_128         16512       25       [4, 128, 512, 512]   float16 
synthesis.L14_512_3.affine    65664       -        [4, 128]             float32 
synthesis.L14_512_3           387         1        [4, 3, 512, 512]     float16 
synthesis                     -           -        [4, 3, 512, 512]     float32 
---                           ---         ---      ---                  ---     
Total                         16665594    5588     -                    -       

Setting up PyTorch plugin "upfirdn2d_plugin"... /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Done.

Discriminator  Parameters  Buffers  Output shape        Datatype
---            ---         ---      ---                 ---     
b512.fromrgb   256         16       [4, 64, 512, 512]   float16 
b512.skip      8192        16       [4, 128, 256, 256]  float16 
b512.conv0     36928       16       [4, 64, 512, 512]   float16 
b512.conv1     73856       16       [4, 128, 256, 256]  float16 
b512           -           16       [4, 128, 256, 256]  float16 
b256.skip      32768       16       [4, 256, 128, 128]  float16 
b256.conv0     147584      16       [4, 128, 256, 256]  float16 
b256.conv1     295168      16       [4, 256, 128, 128]  float16 
b256           -           16       [4, 256, 128, 128]  float16 
b128.skip      131072      16       [4, 512, 64, 64]    float16 
b128.conv0     590080      16       [4, 256, 128, 128]  float16 
b128.conv1     1180160     16       [4, 512, 64, 64]    float16 
b128           -           16       [4, 512, 64, 64]    float16 
b64.skip       262144      16       [4, 512, 32, 32]    float16 
b64.conv0      2359808     16       [4, 512, 64, 64]    float16 
b64.conv1      2359808     16       [4, 512, 32, 32]    float16 
b64            -           16       [4, 512, 32, 32]    float16 
b32.skip       262144      16       [4, 512, 16, 16]    float32 
b32.conv0      2359808     16       [4, 512, 32, 32]    float32 
b32.conv1      2359808     16       [4, 512, 16, 16]    float32 
b32            -           16       [4, 512, 16, 16]    float32 
b16.skip       262144      16       [4, 512, 8, 8]      float32 
b16.conv0      2359808     16       [4, 512, 16, 16]    float32 
b16.conv1      2359808     16       [4, 512, 8, 8]      float32 
b16            -           16       [4, 512, 8, 8]      float32 
b8.skip        262144      16       [4, 512, 4, 4]      float32 
b8.conv0       2359808     16       [4, 512, 8, 8]      float32 
b8.conv1       2359808     16       [4, 512, 4, 4]      float32 
b8             -           16       [4, 512, 4, 4]      float32 
b4.mbstd       -           -        [4, 513, 4, 4]      float32 
b4.conv        2364416     16       [4, 512, 4, 4]      float32 
b4.fc          4194816     -        [4, 512]            float32 
b4.out         513         -        [4, 1]              float32 
---            ---         ---      ---                 ---     
Total          28982849    480      -                   -       

Setting up augmentation...
Distributing across 4 GPUs...
Setting up training phases...
Exporting sample images...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Initializing logs...
Training for 15000 kimg...

tick 0     kimg 0.0      time 1m 58s       sec/tick 12.5    sec/kimg 783.23  maintenance 105.2  cpumem 2.69   gpumem 11.37  reserved 11.96  augment 0.000
Evaluating metrics...
