Loading training set...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."

Num images:  25440
Image shape: [3, 512, 512]
Label shape: [0]

Constructing networks...
Setting up PyTorch plugin "filtered_lrelu_plugin"... /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Done.

Generator                     Parameters  Buffers  Output shape         Datatype
---                           ---         ---      ---                  ---     
mapping.fc0                   262656      -        [8, 512]             float32 
mapping.fc1                   262656      -        [8, 512]             float32 
mapping                       -           512      [8, 16, 512]         float32 
synthesis.input.affine        2052        -        [8, 4]               float32 
synthesis.input               1048576     3081     [8, 1024, 36, 36]    float32 
synthesis.L0_36_1024.affine   525312      -        [8, 1024]            float32 
synthesis.L0_36_1024          1049600     157      [8, 1024, 36, 36]    float32 
synthesis.L1_36_1024.affine   525312      -        [8, 1024]            float32 
synthesis.L1_36_1024          1049600     157      [8, 1024, 36, 36]    float32 
synthesis.L2_52_1024.affine   525312      -        [8, 1024]            float32 
synthesis.L2_52_1024          1049600     169      [8, 1024, 52, 52]    float32 
synthesis.L3_52_1024.affine   525312      -        [8, 1024]            float32 
synthesis.L3_52_1024          1049600     157      [8, 1024, 52, 52]    float32 
synthesis.L4_84_1024.affine   525312      -        [8, 1024]            float32 
synthesis.L4_84_1024          1049600     169      [8, 1024, 84, 84]    float16 
synthesis.L5_84_1024.affine   525312      -        [8, 1024]            float32 
synthesis.L5_84_1024          1049600     157      [8, 1024, 84, 84]    float16 
synthesis.L6_148_1024.affine  525312      -        [8, 1024]            float32 
synthesis.L6_148_1024         1049600     169      [8, 1024, 148, 148]  float16 
synthesis.L7_148_967.affine   525312      -        [8, 1024]            float32 
synthesis.L7_148_967          991175      157      [8, 967, 148, 148]   float16 
synthesis.L8_276_645.affine   496071      -        [8, 967]             float32 
synthesis.L8_276_645          624360      169      [8, 645, 276, 276]   float16 
synthesis.L9_276_431.affine   330885      -        [8, 645]             float32 
synthesis.L9_276_431          278426      157      [8, 431, 276, 276]   float16 
synthesis.L10_532_287.affine  221103      -        [8, 431]             float32 
synthesis.L10_532_287         123984      169      [8, 287, 532, 532]   float16 
synthesis.L11_532_192.affine  147231      -        [8, 287]             float32 
synthesis.L11_532_192         55296       157      [8, 192, 532, 532]   float16 
synthesis.L12_532_128.affine  98496       -        [8, 192]             float32 
synthesis.L12_532_128         24704       25       [8, 128, 532, 532]   float16 
synthesis.L13_512_128.affine  65664       -        [8, 128]             float32 
synthesis.L13_512_128         16512       25       [8, 128, 512, 512]   float16 
synthesis.L14_512_3.affine    65664       -        [8, 128]             float32 
synthesis.L14_512_3           387         1        [8, 3, 512, 512]     float16 
synthesis                     -           -        [8, 3, 512, 512]     float32 
---                           ---         ---      ---                  ---     
Total                         16665594    5588     -                    -       

Setting up PyTorch plugin "upfirdn2d_plugin"... /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Done.

Discriminator  Parameters  Buffers  Output shape        Datatype
---            ---         ---      ---                 ---     
b512.fromrgb   256         16       [8, 64, 512, 512]   float16 
b512.skip      8192        16       [8, 128, 256, 256]  float16 
b512.conv0     36928       16       [8, 64, 512, 512]   float16 
b512.conv1     73856       16       [8, 128, 256, 256]  float16 
b512           -           16       [8, 128, 256, 256]  float16 
b256.skip      32768       16       [8, 256, 128, 128]  float16 
b256.conv0     147584      16       [8, 128, 256, 256]  float16 
b256.conv1     295168      16       [8, 256, 128, 128]  float16 
b256           -           16       [8, 256, 128, 128]  float16 
b128.skip      131072      16       [8, 512, 64, 64]    float16 
b128.conv0     590080      16       [8, 256, 128, 128]  float16 
b128.conv1     1180160     16       [8, 512, 64, 64]    float16 
b128           -           16       [8, 512, 64, 64]    float16 
b64.skip       262144      16       [8, 512, 32, 32]    float16 
b64.conv0      2359808     16       [8, 512, 64, 64]    float16 
b64.conv1      2359808     16       [8, 512, 32, 32]    float16 
b64            -           16       [8, 512, 32, 32]    float16 
b32.skip       262144      16       [8, 512, 16, 16]    float32 
b32.conv0      2359808     16       [8, 512, 32, 32]    float32 
b32.conv1      2359808     16       [8, 512, 16, 16]    float32 
b32            -           16       [8, 512, 16, 16]    float32 
b16.skip       262144      16       [8, 512, 8, 8]      float32 
b16.conv0      2359808     16       [8, 512, 16, 16]    float32 
b16.conv1      2359808     16       [8, 512, 8, 8]      float32 
b16            -           16       [8, 512, 8, 8]      float32 
b8.skip        262144      16       [8, 512, 4, 4]      float32 
b8.conv0       2359808     16       [8, 512, 8, 8]      float32 
b8.conv1       2359808     16       [8, 512, 4, 4]      float32 
b8             -           16       [8, 512, 4, 4]      float32 
b4.mbstd       -           -        [8, 513, 4, 4]      float32 
b4.conv        2364416     16       [8, 512, 4, 4]      float32 
b4.fc          4194816     -        [8, 512]            float32 
b4.out         513         -        [8, 1]              float32 
---            ---         ---      ---                 ---     
Total          28982849    480      -                   -       

Setting up augmentation...
Distributing across 4 GPUs...
Setting up training phases...
Exporting sample images...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Initializing logs...
Training for 10000 kimg...

tick 0     kimg 0.0      time 1m 56s       sec/tick 15.6    sec/kimg 486.24  maintenance 100.3  cpumem 2.50   gpumem 15.32  reserved 17.50  augment 0.000
Evaluating metrics...
{"results": {"fid50k_full": 558.6666576775431}, "metric": "fid50k_full", "total_time": 290.5003705024719, "total_time_str": "4m 51s", "num_gpus": 4, "snapshot_pkl": "network-snapshot-000000.pkl", "timestamp": 1755106782.252983}
tick 1     kimg 10.0     time 11m 05s      sec/tick 251.1   sec/kimg 25.07   maintenance 298.0  cpumem 3.07   gpumem 13.12  reserved 22.29  augment 0.000
tick 2     kimg 20.1     time 15m 17s      sec/tick 252.5   sec/kimg 25.21   maintenance 0.1    cpumem 3.07   gpumem 13.11  reserved 22.29  augment 0.000
tick 3     kimg 30.1     time 19m 32s      sec/tick 254.5   sec/kimg 25.40   maintenance 0.1    cpumem 3.07   gpumem 13.13  reserved 22.29  augment 0.000
tick 4     kimg 40.1     time 23m 47s      sec/tick 254.8   sec/kimg 25.44   maintenance 0.0    cpumem 3.07   gpumem 13.16  reserved 22.29  augment 0.001
tick 5     kimg 50.1     time 28m 01s      sec/tick 254.0   sec/kimg 25.36   maintenance 0.1    cpumem 3.07   gpumem 13.20  reserved 22.29  augment 0.009
tick 6     kimg 60.1     time 32m 19s      sec/tick 258.2   sec/kimg 25.78   maintenance 0.1    cpumem 3.07   gpumem 13.20  reserved 22.29  augment 0.021
tick 7     kimg 70.1     time 36m 32s      sec/tick 252.7   sec/kimg 25.23   maintenance 0.1    cpumem 3.06   gpumem 13.27  reserved 22.29  augment 0.034
tick 8     kimg 80.2     time 40m 45s      sec/tick 252.8   sec/kimg 25.24   maintenance 0.1    cpumem 3.07   gpumem 13.22  reserved 22.29  augment 0.049
tick 9     kimg 90.2     time 45m 02s      sec/tick 256.6   sec/kimg 25.62   maintenance 0.1    cpumem 3.14   gpumem 13.26  reserved 22.29  augment 0.046
tick 10    kimg 100.2    time 49m 20s      sec/tick 258.3   sec/kimg 25.79   maintenance 0.1    cpumem 3.19   gpumem 13.21  reserved 22.29  augment 0.042
tick 11    kimg 110.2    time 53m 37s      sec/tick 257.0   sec/kimg 25.66   maintenance 0.1    cpumem 3.21   gpumem 13.24  reserved 22.29  augment 0.051
tick 12    kimg 120.2    time 57m 52s      sec/tick 254.6   sec/kimg 25.42   maintenance 0.0    cpumem 3.24   gpumem 13.21  reserved 22.29  augment 0.053
tick 13    kimg 130.2    time 1h 02m 15s   sec/tick 262.4   sec/kimg 26.20   maintenance 0.1    cpumem 3.24   gpumem 13.22  reserved 22.29  augment 0.061
tick 14    kimg 140.3    time 1h 06m 30s   sec/tick 255.8   sec/kimg 25.54   maintenance 0.1    cpumem 3.26   gpumem 13.30  reserved 22.29  augment 0.070
tick 15    kimg 150.3    time 1h 10m 46s   sec/tick 255.9   sec/kimg 25.55   maintenance 0.1    cpumem 3.26   gpumem 13.25  reserved 22.29  augment 0.084
tick 16    kimg 160.3    time 1h 15m 06s   sec/tick 260.0   sec/kimg 25.96   maintenance 0.1    cpumem 3.26   gpumem 13.29  reserved 22.29  augment 0.101
tick 17    kimg 170.3    time 1h 19m 25s   sec/tick 258.2   sec/kimg 25.78   maintenance 0.2    cpumem 3.26   gpumem 13.29  reserved 22.29  augment 0.112
tick 18    kimg 180.3    time 1h 23m 46s   sec/tick 261.3   sec/kimg 26.09   maintenance 0.1    cpumem 3.27   gpumem 13.39  reserved 22.29  augment 0.113
