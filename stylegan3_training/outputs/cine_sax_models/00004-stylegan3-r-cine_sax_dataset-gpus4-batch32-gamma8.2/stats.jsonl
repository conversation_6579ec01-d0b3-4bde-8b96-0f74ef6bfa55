{"Loss/scores/fake": {"num": 64, "mean": 0.2576336320489645, "std": 0.19152688962118575}, "Loss/signs/fake": {"num": 64, "mean": 0.875, "std": 0.4841229182759271}, "Loss/G/loss": {"num": 32, "mean": 0.5927890837192535, "std": 0.08273469394920617}, "Loss/scores/real": {"num": 64, "mean": 3.4836456924676895, "std": 2.5238259797020017}, "Loss/signs/real": {"num": 64, "mean": 1.0, "std": 0.0}, "Loss/D/loss": {"num": 32, "mean": 1.1774473190307617, "std": 0.10239394465095764}, "Loss/r1_penalty": {"num": 32, "mean": 9.517144644632936e-05, "std": 1.1074095327700032e-06}, "Loss/D/reg": {"num": 32, "mean": 0.0003902029129676521, "std": 4.540455218433049e-06}, "Progress/tick": {"num": 1, "mean": 0.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 0.03200000151991844, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 115.81033325195312, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 15.559653282165527, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 486.2391662597656, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 100.25067901611328, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 2.5010757446289062, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 15.323419570922852, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 17.501953125, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.0, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.032169535756111145, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.0013403974007815123, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 5581.27392578125, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 2178.04296875, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 7861.232421875, "std": 0.0}, "timestamp": 1755106782.2670982}
{"Loss/scores/fake": {"num": 20032, "mean": -0.3536332974006934, "std": 1.0402596032483244}, "Loss/signs/fake": {"num": 20032, "mean": -0.27655750798722045, "std": 0.9609973698069618}, "Loss/G/loss": {"num": 10016, "mean": 1.0718839816725292, "std": 0.9075547764010369}, "Loss/scores/real": {"num": 10624, "mean": 0.2801124246682129, "std": 1.1065239731969123}, "Loss/signs/real": {"num": 10624, "mean": 0.3045933734939759, "std": 0.9524824811111012}, "Loss/D/loss": {"num": 10016, "mean": 1.3472237891901415, "std": 0.46537182238076585}, "Loss/r1_penalty": {"num": 608, "mean": 0.00025273969148848234, "std": 0.00041969672962166723}, "Loss/D/reg": {"num": 608, "mean": 0.0010362327050308139, "std": 0.0017207565358945307}, "Progress/tick": {"num": 1, "mean": 1.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 10.04800033569336, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 664.880126953125, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 251.09423828125, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.069313049316406, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 297.9755554199219, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.065471649169922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.115694046020508, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.2890625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.00025599999935366213, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.18468892574310303, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.007695371750742197, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 415.40606689453125, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 368.32562255859375, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.201416015625, "std": 0.0}, "timestamp": 1755107033.4687598}
{"Loss/scores/fake": {"num": 20032, "mean": -0.16146338186836756, "std": 1.2980415286441398}, "Loss/signs/fake": {"num": 20032, "mean": -0.23632188498402557, "std": 0.9716748255860069}, "Loss/G/loss": {"num": 10016, "mean": 0.9129284182735894, "std": 1.0110603614121474}, "Loss/scores/real": {"num": 10656, "mean": 0.017501210450724763, "std": 0.973852852030846}, "Loss/signs/real": {"num": 10656, "mean": 0.26407657657657657, "std": 0.9645017167966035}, "Loss/D/loss": {"num": 10016, "mean": 1.45966920599389, "std": 1.2130412935680117}, "Loss/r1_penalty": {"num": 640, "mean": 0.0006124904419721134, "std": 0.0007191986176450828}, "Loss/D/reg": {"num": 640, "mean": 0.0025112107706718235, "std": 0.002948714299331632}, "Progress/tick": {"num": 1, "mean": 2.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 20.06399917602539, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 917.4779052734375, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 252.4864501953125, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.208311080932617, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.11132693290710449, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.065471649169922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.105575561523438, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.2890625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.0, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.2548549771308899, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.010618956759572029, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 416.7751770019531, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 369.2881774902344, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.1256408691406, "std": 0.0}, "timestamp": 1755107286.0655737}
{"Loss/scores/fake": {"num": 20032, "mean": -0.5979133033167241, "std": 1.9162399592649473}, "Loss/signs/fake": {"num": 20032, "mean": -0.4414936102236422, "std": 0.8972643936609179}, "Loss/G/loss": {"num": 10016, "mean": 1.3092099449600274, "std": 1.4956220052641969}, "Loss/scores/real": {"num": 10624, "mean": 0.2594417891963399, "std": 1.1592701322818388}, "Loss/signs/real": {"num": 10624, "mean": 0.39495481927710846, "std": 0.9187005446443289}, "Loss/D/loss": {"num": 10016, "mean": 1.2997255925886548, "std": 0.8049734092030018}, "Loss/r1_penalty": {"num": 608, "mean": 0.0038431625979635435, "std": 0.006241528475325904}, "Loss/D/reg": {"num": 608, "mean": 0.015756966204207856, "std": 0.02559026610057462}, "Progress/tick": {"num": 1, "mean": 3.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 30.079999923706055, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 1172.043701171875, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 254.45274353027344, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.404626846313477, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.11310172080993652, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.065471649169922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.129999160766602, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.2890625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.0, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.32556769251823425, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.013565321452915668, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 424.7593078613281, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 371.7529602050781, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 286.055419921875, "std": 0.0}, "timestamp": 1755107540.5545433}
{"Loss/scores/fake": {"num": 20032, "mean": -1.427791988143858, "std": 1.974152899210768}, "Loss/signs/fake": {"num": 20032, "mean": -0.766573482428115, "std": 0.6421565977532525}, "Loss/G/loss": {"num": 10016, "mean": 2.0607214884850356, "std": 2.019136656104759}, "Loss/scores/real": {"num": 10656, "mean": 1.2061288976432145, "std": 1.7173315017047226}, "Loss/signs/real": {"num": 10656, "mean": 0.5491741741741741, "std": 0.83570791932356}, "Loss/D/loss": {"num": 10016, "mean": 0.924523718393268, "std": 0.7648172688106238}, "Loss/r1_penalty": {"num": 640, "mean": 0.01750195529384655, "std": 0.02198221378346496}, "Loss/D/reg": {"num": 640, "mean": 0.0717580158408964, "std": 0.09012707476541494}, "Progress/tick": {"num": 1, "mean": 4.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 40.09600067138672, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 1426.892333984375, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 254.82688903808594, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.441980361938477, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.02164912223815918, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.065471649169922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.164281845092773, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.2890625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.0007680000271648169, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.3963589668273926, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.016514956951141357, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 420.2639465332031, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 370.94195556640625, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.59051513671875, "std": 0.0}, "timestamp": 1755107795.4815097}
{"Loss/scores/fake": {"num": 20032, "mean": -2.164077837293158, "std": 2.1745722929897164}, "Loss/signs/fake": {"num": 20032, "mean": -0.8451477635782748, "std": 0.5345327470966026}, "Loss/G/loss": {"num": 10016, "mean": 2.6324645665506967, "std": 1.9577772261950552}, "Loss/scores/real": {"num": 10624, "mean": 2.1983443919922707, "std": 2.2425681039395577}, "Loss/signs/real": {"num": 10624, "mean": 0.7304216867469879, "std": 0.682996456454706}, "Loss/D/loss": {"num": 10016, "mean": 0.6967427531800667, "std": 0.9057070774475892}, "Loss/r1_penalty": {"num": 608, "mean": 0.012660138004943483, "std": 0.013805896503307005}, "Loss/D/reg": {"num": 608, "mean": 0.05190656442611821, "std": 0.056604173579663}, "Progress/tick": {"num": 1, "mean": 5.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 50.11199951171875, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 1681.0174560546875, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 254.0251922607422, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.361940383911133, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.1000375747680664, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.065471649169922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.198614120483398, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.2890625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.009472002275288105, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.46694931387901306, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.019456220790743828, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 422.33447265625, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 369.94970703125, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.93560791015625, "std": 0.0}, "timestamp": 1755108049.6059396}
{"Loss/scores/fake": {"num": 20032, "mean": -2.417373876602124, "std": 1.6213249999705492}, "Loss/signs/fake": {"num": 20032, "mean": -0.8774960063897763, "std": 0.47958394340302263}, "Loss/G/loss": {"num": 10016, "mean": 2.8114867530667933, "std": 1.4487795698424053}, "Loss/scores/real": {"num": 10656, "mean": 2.6917452496342227, "std": 2.2127970113405784}, "Loss/signs/real": {"num": 10656, "mean": 0.7608858858858859, "std": 0.6488857130956504}, "Loss/D/loss": {"num": 10016, "mean": 0.5329849154178125, "std": 0.7295305146576807}, "Loss/r1_penalty": {"num": 640, "mean": 0.046944439300568776, "std": 0.15978243585441257}, "Loss/D/reg": {"num": 640, "mean": 0.19247219455428421, "std": 0.6551079581594045}, "Progress/tick": {"num": 1, "mean": 6.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 60.12799835205078, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 1939.3626708984375, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 258.2390441894531, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.78265380859375, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10607004165649414, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.065471649169922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.199995040893555, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.02124800533056259, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.5387118458747864, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.022446326911449432, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 421.5623779296875, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 368.9942932128906, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.3509216308594, "std": 0.0}, "timestamp": 1755108307.951952}
{"Loss/scores/fake": {"num": 20032, "mean": -2.552078513196482, "std": 1.6945089240933424}, "Loss/signs/fake": {"num": 20032, "mean": -0.8816892971246006, "std": 0.47183046037313836}, "Loss/G/loss": {"num": 10016, "mean": 2.945606692143667, "std": 1.450059047250757}, "Loss/scores/real": {"num": 10624, "mean": 3.0127683040259563, "std": 2.30538580818961}, "Loss/signs/real": {"num": 10624, "mean": 0.7660015060240963, "std": 0.6428387766530705}, "Loss/D/loss": {"num": 10016, "mean": 0.5055027878694475, "std": 0.7187179821010047}, "Loss/r1_penalty": {"num": 608, "mean": 0.01689583136574488, "std": 0.0213811482565266}, "Loss/D/reg": {"num": 608, "mean": 0.06927290636935811, "std": 0.08766270465388203}, "Progress/tick": {"num": 1, "mean": 7.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 70.14399719238281, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 2192.213623046875, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 252.7029266357422, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.22992515563965, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.14800000190734863, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.064495086669922, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.271856307983398, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.03430398926138878, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.6089482307434082, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.025372842326760292, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 418.6961975097656, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 368.2723693847656, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 286.6882629394531, "std": 0.0}, "timestamp": 1755108560.7265065}
{"Loss/scores/fake": {"num": 20032, "mean": -3.043831054138365, "std": 2.4276233646801697}, "Loss/signs/fake": {"num": 20032, "mean": -0.9164337060702875, "std": 0.4001865344789575}, "Loss/G/loss": {"num": 10016, "mean": 3.3907416219742723, "std": 2.5116764003510643}, "Loss/scores/real": {"num": 10656, "mean": 3.3843186660487956, "std": 2.3045310426906003}, "Loss/signs/real": {"num": 10656, "mean": 0.8207582582582582, "std": 0.5712756615688002}, "Loss/D/loss": {"num": 10016, "mean": 0.39963004369282734, "std": 0.6354613009943544}, "Loss/r1_penalty": {"num": 640, "mean": 0.017948558705393226, "std": 0.032132483360830924}, "Loss/D/reg": {"num": 640, "mean": 0.07358908848837017, "std": 0.13174317830467283}, "Progress/tick": {"num": 1, "mean": 8.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 80.16000366210938, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 2445.125244140625, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 252.84889221191406, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.244497299194336, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.06287240982055664, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.067249298095703, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.222746849060059, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.049151886254549026, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.6792014837265015, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.02830006182193756, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 418.59173583984375, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 369.1827087402344, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 287.2063903808594, "std": 0.0}, "timestamp": 1755108813.713874}
{"Loss/scores/fake": {"num": 20032, "mean": -1.475725378445424, "std": 1.8312173331942638}, "Loss/signs/fake": {"num": 20032, "mean": -0.6367811501597445, "std": 0.7710445945606732}, "Loss/G/loss": {"num": 10016, "mean": 2.044725759904802, "std": 1.6361189980468935}, "Loss/scores/real": {"num": 10656, "mean": 1.5286528522801772, "std": 2.1414674002383918}, "Loss/signs/real": {"num": 10656, "mean": 0.524024024024024, "std": 0.8517034825839737}, "Loss/D/loss": {"num": 10016, "mean": 0.9285872229798949, "std": 0.7606571690523869}, "Loss/r1_penalty": {"num": 640, "mean": 0.007365573274728377, "std": 0.020193375092401496}, "Loss/D/reg": {"num": 640, "mean": 0.03019885016401531, "std": 0.0827928369000349}, "Progress/tick": {"num": 1, "mean": 9.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 90.1760025024414, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 2701.869140625, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 256.63970947265625, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.622974395751953, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10400080680847168, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.1398468017578125, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.262216567993164, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.046079907566308975, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.7505191564559937, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.03127163276076317, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 419.1098937988281, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 370.187255859375, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 286.2827453613281, "std": 0.0}, "timestamp": 1755109070.4576054}
{"Loss/scores/fake": {"num": 20032, "mean": -1.0521424411395832, "std": 1.5601896984923096}, "Loss/signs/fake": {"num": 20032, "mean": -0.6487619808306709, "std": 0.7609913877493385}, "Loss/G/loss": {"num": 10016, "mean": 1.6491722910644147, "std": 1.548891474946913}, "Loss/scores/real": {"num": 10624, "mean": 0.8665092716355393, "std": 1.4858579583576021}, "Loss/signs/real": {"num": 10624, "mean": 0.42808734939759036, "std": 0.9037373630019649}, "Loss/D/loss": {"num": 10016, "mean": 1.0366094525659475, "std": 0.6563882860067686}, "Loss/r1_penalty": {"num": 608, "mean": 0.009490073666950738, "std": 0.011185171698187337}, "Loss/D/reg": {"num": 608, "mean": 0.038909300919744726, "std": 0.04585920322204072}, "Progress/tick": {"num": 1, "mean": 10.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 100.19200134277344, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 2960.2626953125, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 258.29278564453125, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.788015365600586, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10090827941894531, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.1911354064941406, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.212315559387207, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.04198393598198891, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.8222951889038086, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.03426229953765869, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 419.2389221191406, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 369.1243591308594, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 287.1244812011719, "std": 0.0}, "timestamp": 1755109328.8506973}
{"Loss/scores/fake": {"num": 20032, "mean": -2.488582972821723, "std": 3.026820812845802}, "Loss/signs/fake": {"num": 20032, "mean": -0.8586261980830671, "std": 0.5126022356227269}, "Loss/G/loss": {"num": 10016, "mean": 2.9309938083262965, "std": 2.872464453647505}, "Loss/scores/real": {"num": 10656, "mean": 2.5970445614442363, "std": 2.2748590541567233}, "Loss/signs/real": {"num": 10656, "mean": 0.7366741741741741, "std": 0.676247854787576}, "Loss/D/loss": {"num": 10016, "mean": 0.5888878436849866, "std": 0.7625583837149353}, "Loss/r1_penalty": {"num": 640, "mean": 0.014259522675274638, "std": 0.018946941033821166}, "Loss/D/reg": {"num": 640, "mean": 0.058464041768456806, "std": 0.07768245593434521}, "Progress/tick": {"num": 1, "mean": 11.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 110.20800018310547, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 3217.3623046875, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 256.99896240234375, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.658842086791992, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10059690475463867, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.2087440490722656, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.238214492797852, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.051455870270729065, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.8937117457389832, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.0372379906475544, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 418.5671691894531, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 369.5278015136719, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 287.3067626953125, "std": 0.0}, "timestamp": 1755109585.8729832}
{"Loss/scores/fake": {"num": 20032, "mean": -1.4396155757395128, "std": 1.5076677127371216}, "Loss/signs/fake": {"num": 20032, "mean": -0.7404153354632588, "std": 0.6721496343901632}, "Loss/G/loss": {"num": 10016, "mean": 1.9853624606760927, "std": 1.3411075904929046}, "Loss/scores/real": {"num": 10624, "mean": 1.33951544974291, "std": 1.6897368144439175}, "Loss/signs/real": {"num": 10624, "mean": 0.5724774096385542, "std": 0.8199204933733094}, "Loss/D/loss": {"num": 10016, "mean": 0.8636346652764625, "std": 0.6702757941259033}, "Loss/r1_penalty": {"num": 608, "mean": 0.01807639652274941, "std": 0.021100303266888328}, "Loss/D/reg": {"num": 608, "mean": 0.07411322415512252, "std": 0.08651123900390822}, "Progress/tick": {"num": 1, "mean": 12.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 120.2239990234375, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 3472.0048828125, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 254.6215362548828, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.421480178833008, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.021176815032958984, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.237689971923828, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.20817756652832, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.05299185961484909, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 0.9644458293914795, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.040185242891311646, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 426.9823913574219, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 372.01611328125, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 286.28173828125, "std": 0.0}, "timestamp": 1755109840.5970428}
{"Loss/scores/fake": {"num": 20032, "mean": -2.303405095138751, "std": 1.8941529751870976}, "Loss/signs/fake": {"num": 20032, "mean": -0.8295726837060703, "std": 0.5583987486095472}, "Loss/G/loss": {"num": 10016, "mean": 2.7750122959490495, "std": 1.6257043171752938}, "Loss/scores/real": {"num": 10656, "mean": 2.148147028417805, "std": 1.9456725829906012}, "Loss/signs/real": {"num": 10656, "mean": 0.7055180180180181, "std": 0.7086919826355648}, "Loss/D/loss": {"num": 10016, "mean": 0.6130995003688045, "std": 0.6547088547890715}, "Loss/r1_penalty": {"num": 640, "mean": 0.01956048969004769, "std": 0.019058411392427598}, "Loss/D/reg": {"num": 640, "mean": 0.08019800574984401, "std": 0.07813948529237763}, "Progress/tick": {"num": 1, "mean": 13.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 130.24000549316406, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 3734.51806640625, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 262.400634765625, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 26.19814682006836, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.11235952377319336, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.240245819091797, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.224185943603516, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.06118380278348923, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 1.0373661518096924, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.04322358965873718, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 434.68084716796875, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 372.0816650390625, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.52191162109375, "std": 0.0}, "timestamp": 1755110103.109485}
{"Loss/scores/fake": {"num": 20032, "mean": -1.8934822471086352, "std": 1.5944539263581208}, "Loss/signs/fake": {"num": 20032, "mean": -0.792332268370607, "std": 0.6100898101908345}, "Loss/G/loss": {"num": 10016, "mean": 2.3471661776470873, "std": 1.3605785356867128}, "Loss/scores/real": {"num": 10624, "mean": 1.7882108909122825, "std": 1.717359704570313}, "Loss/signs/real": {"num": 10624, "mean": 0.6837349397590361, "std": 0.7297304517098812}, "Loss/D/loss": {"num": 10016, "mean": 0.6835424151604834, "std": 0.6399543319334713}, "Loss/r1_penalty": {"num": 608, "mean": 0.019561363010373162, "std": 0.01949098840387078}, "Loss/D/reg": {"num": 608, "mean": 0.08020158631629065, "std": 0.07991304952825361}, "Progress/tick": {"num": 1, "mean": 14.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 140.25599670410156, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 3990.423095703125, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 255.79833984375, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.538972854614258, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10676312446594238, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.26214599609375, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.302940368652344, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.06988784670829773, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 1.1084508895874023, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.046185452491045, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 425.0583190917969, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 371.2921447753906, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 286.3656921386719, "std": 0.0}, "timestamp": 1755110359.0141816}
{"Loss/scores/fake": {"num": 20032, "mean": -2.7351096343879884, "std": 2.5134386920813183}, "Loss/signs/fake": {"num": 20032, "mean": -0.8475439297124601, "std": 0.5307252464388336}, "Loss/G/loss": {"num": 10016, "mean": 3.0939368989806586, "std": 2.382085665002459}, "Loss/scores/real": {"num": 10656, "mean": 2.773312747749771, "std": 2.2238759556685905}, "Loss/signs/real": {"num": 10656, "mean": 0.7678303303303303, "std": 0.6406532477282979}, "Loss/D/loss": {"num": 10016, "mean": 0.5023338528338466, "std": 0.6210395904172616}, "Loss/r1_penalty": {"num": 640, "mean": 0.01849446230335161, "std": 0.029795066624521204}, "Loss/D/reg": {"num": 640, "mean": 0.07582729429705068, "std": 0.12215977076492988}, "Progress/tick": {"num": 1, "mean": 15.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 150.27200317382812, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 4246.40234375, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 255.87844848632812, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.546968460083008, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10077309608459473, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.264392852783203, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.250545501708984, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.291015625, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.08396795392036438, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 1.1795562505722046, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.04914817586541176, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 427.4288635253906, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 370.8590087890625, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 285.5485534667969, "std": 0.0}, "timestamp": 1755110614.9142392}
{"Loss/scores/fake": {"num": 20032, "mean": -3.734640820744855, "std": 3.7766544375551065}, "Loss/signs/fake": {"num": 20032, "mean": -0.9193290734824281, "std": 0.39348958645679605}, "Loss/G/loss": {"num": 10016, "mean": 4.02025564174397, "std": 4.315239171778995}, "Loss/scores/real": {"num": 10656, "mean": 3.9700287227370628, "std": 2.3398093231397574}, "Loss/signs/real": {"num": 10656, "mean": 0.8607357357357357, "std": 0.5090520535539188}, "Loss/D/loss": {"num": 10016, "mean": 0.3135764680037754, "std": 0.5492191816390969}, "Loss/r1_penalty": {"num": 640, "mean": 0.0170343356061494, "std": 0.0336555445827634}, "Loss/D/reg": {"num": 640, "mean": 0.06984077389352024, "std": 0.13798772978725712}, "Progress/tick": {"num": 1, "mean": 16.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 160.28799438476562, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 4506.45556640625, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 260.00152587890625, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.958620071411133, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.05189013481140137, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.2646026611328125, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.286935806274414, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.29296875, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.10086408257484436, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 1.2517932653427124, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.05215805396437645, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 430.6913146972656, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 371.962890625, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 287.21868896484375, "std": 0.0}, "timestamp": 1755110875.1908658}
{"Loss/scores/fake": {"num": 20032, "mean": -1.843094982699262, "std": 1.3307372502262242}, "Loss/signs/fake": {"num": 20032, "mean": -0.8442492012779552, "std": 0.5359508243687424}, "Loss/G/loss": {"num": 10016, "mean": 2.2205038817641074, "std": 1.1759045272244222}, "Loss/scores/real": {"num": 10624, "mean": 2.0758846427358866, "std": 1.875517002821333}, "Loss/signs/real": {"num": 10624, "mean": 0.7010542168674698, "std": 0.713107975703777}, "Loss/D/loss": {"num": 10016, "mean": 0.5904108798428894, "std": 0.5263109256007079}, "Loss/r1_penalty": {"num": 608, "mean": 0.03288653720856497, "std": 0.029907820996388477}, "Loss/D/reg": {"num": 608, "mean": 0.13483479944989085, "std": 0.12262206173446392}, "Progress/tick": {"num": 1, "mean": 17.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 170.3040008544922, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 4764.88916015625, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 258.1881408691406, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 25.777570724487305, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.2454226016998291, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.264812469482422, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.293949127197266, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.29296875, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.11212816834449768, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 1.3235803842544556, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.05514918267726898, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 432.0665588378906, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 373.28179931640625, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 287.20843505859375, "std": 0.0}, "timestamp": 1755111133.4802315}
{"Loss/scores/fake": {"num": 20032, "mean": -1.3217028328396094, "std": 1.2773450108634952}, "Loss/signs/fake": {"num": 20032, "mean": -0.7264376996805112, "std": 0.6872323249694294}, "Loss/G/loss": {"num": 10016, "mean": 1.8064241138890909, "std": 1.0823548310099134}, "Loss/scores/real": {"num": 10656, "mean": 1.3137892327906266, "std": 1.547788477306875}, "Loss/signs/real": {"num": 10656, "mean": 0.5853978978978979, "std": 0.8107461385271733}, "Loss/D/loss": {"num": 10016, "mean": 0.8210876661534317, "std": 0.5396360901291027}, "Loss/r1_penalty": {"num": 640, "mean": 0.024973828465954286, "std": 0.023627729786519527}, "Loss/D/reg": {"num": 640, "mean": 0.10239269459125352, "std": 0.09687368871525662}, "Progress/tick": {"num": 1, "mean": 18.0, "std": 0.0}, "Progress/kimg": {"num": 1, "mean": 180.32000732421875, "std": 0.0}, "Timing/total_sec": {"num": 1, "mean": 5026.28759765625, "std": 0.0}, "Timing/sec_per_tick": {"num": 1, "mean": 261.295654296875, "std": 0.0}, "Timing/sec_per_kimg": {"num": 1, "mean": 26.087825775146484, "std": 0.0}, "Timing/maintenance_sec": {"num": 1, "mean": 0.10262703895568848, "std": 0.0}, "Resources/cpu_mem_gb": {"num": 1, "mean": 3.2650184631347656, "std": 0.0}, "Resources/peak_gpu_mem_gb": {"num": 1, "mean": 13.386215209960938, "std": 0.0}, "Resources/peak_gpu_mem_reserved_gb": {"num": 1, "mean": 22.29296875, "std": 0.0}, "Progress/augment": {"num": 1, "mean": 0.1134081780910492, "std": 0.0}, "Timing/total_hours": {"num": 1, "mean": 1.3961910009384155, "std": 0.0}, "Timing/total_days": {"num": 1, "mean": 0.05817462503910065, "std": 0.0}, "Timing/Gboth": {"num": 1, "mean": 471.7926330566406, "std": 0.0}, "Timing/Dmain": {"num": 1, "mean": 386.8651428222656, "std": 0.0}, "Timing/Dreg": {"num": 1, "mean": 293.8767395019531, "std": 0.0}, "timestamp": 1755111394.893456}
