{"G_kwargs": {"class_name": "training.networks_stylegan3.Generator", "z_dim": 512, "w_dim": 512, "mapping_kwargs": {"num_layers": 2}, "channel_base": 65536, "channel_max": 1024, "magnitude_ema_beta": 0.9988915792636801, "conv_kernel": 1, "use_radial_filters": true}, "D_kwargs": {"class_name": "training.networks_stylegan2.Discriminator", "block_kwargs": {"freeze_layers": 0}, "mapping_kwargs": {}, "epilogue_kwargs": {"mbstd_group_size": 4}, "channel_base": 32768, "channel_max": 512}, "G_opt_kwargs": {"class_name": "torch.optim.Adam", "betas": [0, 0.99], "eps": 1e-08, "lr": 0.0025}, "D_opt_kwargs": {"class_name": "torch.optim.Adam", "betas": [0, 0.99], "eps": 1e-08, "lr": 0.002}, "loss_kwargs": {"class_name": "training.loss.StyleGAN2Loss", "r1_gamma": 8.2, "blur_init_sigma": 10, "blur_fade_kimg": 200.0}, "data_loader_kwargs": {"pin_memory": true, "prefetch_factor": 2, "num_workers": 3}, "training_set_kwargs": {"class_name": "training.dataset.ImageFolderDataset", "path": "../data/cine_sax_dataset.zip", "use_labels": false, "max_size": 12720, "xflip": true, "resolution": 512, "random_seed": 42}, "num_gpus": 4, "batch_size": 32, "batch_gpu": 8, "metrics": ["fid50k_full"], "total_kimg": 10000, "kimg_per_tick": 10, "image_snapshot_ticks": 50, "network_snapshot_ticks": 50, "random_seed": 42, "ema_kimg": 10.0, "augment_kwargs": {"class_name": "training.augment.AugmentPipe", "xflip": 1, "rotate90": 1, "xint": 1, "scale": 1, "rotate": 1, "aniso": 1, "xfrac": 1, "brightness": 1, "contrast": 1, "lumaflip": 1, "hue": 1, "saturation": 1}, "ada_target": 0.6, "run_dir": "../outputs/cine_sax_models/00004-stylegan3-r-cine_sax_dataset-gpus4-batch32-gamma8.2"}