/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
Loading training set...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."

Num images:  2022
Image shape: [3, 512, 512]
Label shape: [0]

Constructing networks...
Setting up PyTorch plugin "filtered_lrelu_plugin"... /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Done.

Generator                     Parameters  Buffers  Output shape         Datatype
---                           ---         ---      ---                  ---     
mapping.fc0                   262656      -        [4, 512]             float32 
mapping.fc1                   262656      -        [4, 512]             float32 
mapping                       -           512      [4, 16, 512]         float32 
synthesis.input.affine        2052        -        [4, 4]               float32 
synthesis.input               1048576     3081     [4, 1024, 36, 36]    float32 
synthesis.L0_36_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L0_36_1024          1049600     157      [4, 1024, 36, 36]    float32 
synthesis.L1_36_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L1_36_1024          1049600     157      [4, 1024, 36, 36]    float32 
synthesis.L2_52_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L2_52_1024          1049600     169      [4, 1024, 52, 52]    float32 
synthesis.L3_52_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L3_52_1024          1049600     157      [4, 1024, 52, 52]    float32 
synthesis.L4_84_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L4_84_1024          1049600     169      [4, 1024, 84, 84]    float16 
synthesis.L5_84_1024.affine   525312      -        [4, 1024]            float32 
synthesis.L5_84_1024          1049600     157      [4, 1024, 84, 84]    float16 
synthesis.L6_148_1024.affine  525312      -        [4, 1024]            float32 
synthesis.L6_148_1024         1049600     169      [4, 1024, 148, 148]  float16 
synthesis.L7_148_967.affine   525312      -        [4, 1024]            float32 
synthesis.L7_148_967          991175      157      [4, 967, 148, 148]   float16 
synthesis.L8_276_645.affine   496071      -        [4, 967]             float32 
synthesis.L8_276_645          624360      169      [4, 645, 276, 276]   float16 
synthesis.L9_276_431.affine   330885      -        [4, 645]             float32 
synthesis.L9_276_431          278426      157      [4, 431, 276, 276]   float16 
synthesis.L10_532_287.affine  221103      -        [4, 431]             float32 
synthesis.L10_532_287         123984      169      [4, 287, 532, 532]   float16 
synthesis.L11_532_192.affine  147231      -        [4, 287]             float32 
synthesis.L11_532_192         55296       157      [4, 192, 532, 532]   float16 
synthesis.L12_532_128.affine  98496       -        [4, 192]             float32 
synthesis.L12_532_128         24704       25       [4, 128, 532, 532]   float16 
synthesis.L13_512_128.affine  65664       -        [4, 128]             float32 
synthesis.L13_512_128         16512       25       [4, 128, 512, 512]   float16 
synthesis.L14_512_3.affine    65664       -        [4, 128]             float32 
synthesis.L14_512_3           387         1        [4, 3, 512, 512]     float16 
synthesis                     -           -        [4, 3, 512, 512]     float32 
---                           ---         ---      ---                  ---     
Total                         16665594    5588     -                    -       

Setting up PyTorch plugin "upfirdn2d_plugin"... /home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Done.

Discriminator  Parameters  Buffers  Output shape        Datatype
---            ---         ---      ---                 ---     
b512.fromrgb   256         16       [4, 64, 512, 512]   float16 
b512.skip      8192        16       [4, 128, 256, 256]  float16 
b512.conv0     36928       16       [4, 64, 512, 512]   float16 
b512.conv1     73856       16       [4, 128, 256, 256]  float16 
b512           -           16       [4, 128, 256, 256]  float16 
b256.skip      32768       16       [4, 256, 128, 128]  float16 
b256.conv0     147584      16       [4, 128, 256, 256]  float16 
b256.conv1     295168      16       [4, 256, 128, 128]  float16 
b256           -           16       [4, 256, 128, 128]  float16 
b128.skip      131072      16       [4, 512, 64, 64]    float16 
b128.conv0     590080      16       [4, 256, 128, 128]  float16 
b128.conv1     1180160     16       [4, 512, 64, 64]    float16 
b128           -           16       [4, 512, 64, 64]    float16 
b64.skip       262144      16       [4, 512, 32, 32]    float16 
b64.conv0      2359808     16       [4, 512, 64, 64]    float16 
b64.conv1      2359808     16       [4, 512, 32, 32]    float16 
b64            -           16       [4, 512, 32, 32]    float16 
b32.skip       262144      16       [4, 512, 16, 16]    float32 
b32.conv0      2359808     16       [4, 512, 32, 32]    float32 
b32.conv1      2359808     16       [4, 512, 16, 16]    float32 
b32            -           16       [4, 512, 16, 16]    float32 
b16.skip       262144      16       [4, 512, 8, 8]      float32 
b16.conv0      2359808     16       [4, 512, 16, 16]    float32 
b16.conv1      2359808     16       [4, 512, 8, 8]      float32 
b16            -           16       [4, 512, 8, 8]      float32 
b8.skip        262144      16       [4, 512, 4, 4]      float32 
b8.conv0       2359808     16       [4, 512, 8, 8]      float32 
b8.conv1       2359808     16       [4, 512, 4, 4]      float32 
b8             -           16       [4, 512, 4, 4]      float32 
b4.mbstd       -           -        [4, 513, 4, 4]      float32 
b4.conv        2364416     16       [4, 512, 4, 4]      float32 
b4.fc          4194816     -        [4, 512]            float32 
b4.out         513         -        [4, 1]              float32 
---            ---         ---      ---                 ---     
Total          28982849    480      -                   -       

Setting up augmentation...
Distributing across 4 GPUs...
Setting up training phases...
Exporting sample images...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/cpp_extension.py:1965: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. 
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Initializing logs...
Training for 15000 kimg...

tick 0     kimg 0.0      time 1m 58s       sec/tick 12.5    sec/kimg 783.23  maintenance 105.2  cpumem 2.69   gpumem 11.37  reserved 11.96  augment 0.000
Evaluating metrics...
