Loading training set...
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."
/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/sampler.py:65: UserWarning: `data_source` argument is not used and will be removed in 2.2.0.You may still have custom implementation that utilizes it.
  warnings.warn("`data_source` argument is not used and will be removed in 2.2.0."

Num images:  2022
Image shape: [3, 512, 512]
Label shape: [0]

Constructing networks...
Resuming from "../outputs/t2map_models/network-snapshot-*.pkl"
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/multiprocessing/spawn.py", line 76, in _wrap
    fn(i, *args)
  File "/common/lidxxlab/cmrchallenge/task3/stylegan3_training/stylegan3/train.py", line 47, in subprocess_fn
    training_loop.training_loop(rank=rank, **c)
  File "/common/lidxxlab/cmrchallenge/task3/stylegan3_training/stylegan3/training/training_loop.py", line 159, in training_loop
    with dnnlib.util.open_url(resume_pkl) as f:
  File "/common/lidxxlab/cmrchallenge/task3/stylegan3_training/stylegan3/dnnlib/util.py", line 403, in open_url
    return url if return_filename else open(url, "rb")
FileNotFoundError: [Errno 2] No such file or directory: '../outputs/t2map_models/network-snapshot-*.pkl'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/multiprocessing/spawn.py", line 85, in _wrap
    sys.exit(1)
SystemExit: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/process.py", line 318, in _bootstrap
    util._exit_function()
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/util.py", line 357, in _exit_function
    p.join()
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/process.py", line 149, in join
    res = self._popen.wait(timeout)
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/popen_fork.py", line 47, in wait
    return self.poll(os.WNOHANG if timeout == 0.0 else 0)
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/multiprocessing/popen_fork.py", line 27, in poll
    pid, sts = os.waitpid(self.pid, flag)
  File "/home/<USER>/.conda/envs/promptmr/lib/python3.8/site-packages/torch/utils/data/_utils/signal_handling.py", line 67, in handler
    _error_if_any_worker_fails()
RuntimeError: DataLoader worker (pid 2521186) is killed by signal: Terminated. 
