#!/bin/bash
#SBATCH --job-name=stylegan3_cine_sax
#SBATCH --partition=gpu
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=16
#SBATCH --mem=200G
#SBATCH --time=256:00:00
#SBATCH --gres=gpu:l40s:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/stylegan3_training/logs/stylegan3_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/stylegan3_training/logs/stylegan3_%j.err

# 工作目录
WORKDIR=/common/lidxxlab/cmrchallenge/task3/stylegan3_training
cd $WORKDIR

# 1) 先清理已加载模块（避免版本冲突）
module purge

# 2) 加载GCC 9.5.0（满足PyTorch要求）
module load gcc11/11.3.0

# 3) 加载CUDA Toolkit 12.1.1
module load cuda12.1/toolkit/12.1.1

# 4) 激活conda环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr

# Debug: 打印环境信息
echo "=== 计算节点环境检查 ==="
echo "节点名称: $(hostname)"
echo "Python路径: $(which python)"
echo "GCC版本: $(gcc --version | head -1)"
echo "G++版本: $(g++ --version | head -1)"
echo "CUDA可用性: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "CUDA版本: $(python -c 'import torch; print(torch.version.cuda)')"
echo "PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"

# 检查CUDA工具链
echo -e "\n=== CUDA工具链检查 ==="
echo "nvcc路径: $(which nvcc)"
echo "nvcc版本:"
nvcc --version
echo "CUDA_HOME: $CUDA_HOME"

# 设置CUDA环境变量
export CUDA_HOME=$(dirname $(dirname $(which nvcc)))
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# 设置CUDA架构（L40S GPU）
export TORCH_CUDA_ARCH_LIST="8.9"

# 设置编译器环境变量
export CC=$(which gcc)
export CXX=$(which g++)

# 清理编译缓存
echo -e "\n=== 清理编译缓存 ==="
rm -rf ~/.cache/torch_extensions

echo "=== 最终环境变量 ==="
echo "CUDA_HOME: $CUDA_HOME"
echo "TORCH_CUDA_ARCH_LIST: $TORCH_CUDA_ARCH_LIST"
echo "CC: $CC"
echo "CXX: $CXX"

# 安装StyleGAN3依赖
echo "=== 安装StyleGAN3依赖 ==="
pip install click requests tqdm pyspng ninja imageio-ffmpeg==0.4.3

# 步骤1: 数据预处理
echo "=== Step 1: Data Preprocessing ==="
python scripts/preprocess_cine_sax.py \
    --input_dir /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/data/data_category/cine_sax \
    --output_dir data/cine_sax_processed \
    --target_shape 512 512

# 步骤2: StyleGAN3训练
echo "=== Step 2: StyleGAN3 Training ==="
cd stylegan3

# 检查数据集
if [ ! -f "../data/cine_sax_dataset.zip" ]; then
    echo "Creating StyleGAN3 dataset..."
    python dataset_tool.py \
        --source ../data/cine_sax_processed \
        --dest ../data/cine_sax_dataset.zip \
        --resolution=512x512
fi

echo "开始StyleGAN3训练..."
python train.py \
    --outdir ../outputs/cine_sax_models \
    --cfg stylegan3-r \
    --data ../data/cine_sax_dataset.zip \
    --gpus 4 \
    --batch 32 \
    --gamma 8.2 \
    --mirror 1 \
    --kimg 10000 \
    --snap 50 \
    --tick 10 \
    --metrics fid50k_full \
    --seed 42

echo "StyleGAN3训练完成！"

# 生成样本
echo "=== Step 3: Generate Sample Images ==="
cd ..
python scripts/check_training_status.py > /tmp/model_check.txt
LATEST_MODEL=$(grep "Latest model:" /tmp/model_check.txt | cut -d' ' -f3-)

if [ -n "$LATEST_MODEL" ] && [ -f "$LATEST_MODEL" ]; then
    echo "Using model: $LATEST_MODEL"
    cd stylegan3
    python gen_images.py \
        --outdir ../outputs/samples \
        --seeds 0-63 \
        --network "../$LATEST_MODEL"
    echo "Sample generation completed!"
else
    echo "❌ No trained model found"
fi
