#!/usr/bin/env python3
"""
检查T2map StyleGAN3训练状态和结果
"""

import os
import glob
import pickle
from pathlib import Path
import argparse

def check_training_status():
    """检查训练状态"""
    print("=== T2map StyleGAN3 Training Status ===\n")
    
    # 检查数据预处理
    processed_dir = Path("data/t2map_processed")
    if processed_dir.exists():
        png_files = list(processed_dir.glob("*.png"))
        print(f"✅ 预处理数据: {len(png_files)} 张图像")
    else:
        print("❌ 预处理数据不存在")
        return
    
    # 检查数据集
    dataset_path = Path("data/t2map_dataset.zip")
    if dataset_path.exists():
        size_mb = dataset_path.stat().st_size / (1024 * 1024)
        print(f"✅ StyleGAN3数据集: {size_mb:.1f} MB")
    else:
        print("❌ StyleGAN3数据集不存在")
        return
    
    # 检查模型
    models_dir = Path("outputs/t2map_models")
    if models_dir.exists():
        pkl_files = sorted(models_dir.glob("network-snapshot-*.pkl"))
        if pkl_files:
            print(f"✅ 训练模型: {len(pkl_files)} 个检查点")
            latest_model = pkl_files[-1]
            print(f"   最新模型: {latest_model.name}")
            
            # 尝试读取模型信息
            try:
                with open(latest_model, 'rb') as f:
                    data = pickle.load(f)
                    if 'training_set_kwargs' in data:
                        training_info = data['training_set_kwargs']
                        print(f"   训练分辨率: {training_info.get('resolution', 'Unknown')}")
                    if 'cur_nimg' in data:
                        print(f"   训练图像数: {data['cur_nimg']:,}")
            except Exception as e:
                print(f"   模型信息读取失败: {e}")
        else:
            print("❌ 未找到训练模型")
    else:
        print("❌ 模型目录不存在")
    
    # 检查生成样本
    samples_dir = Path("outputs/t2map_samples")
    if samples_dir.exists():
        sample_files = list(samples_dir.rglob("*.png"))
        print(f"✅ 生成样本: {len(sample_files)} 张图像")
        
        # 按子目录统计
        subdirs = [d for d in samples_dir.iterdir() if d.is_dir()]
        for subdir in subdirs:
            count = len(list(subdir.glob("*.png")))
            print(f"   {subdir.name}: {count} 张")
    else:
        print("❌ 样本目录不存在")
    
    # 检查日志
    logs_dir = Path("logs")
    if logs_dir.exists():
        log_files = list(logs_dir.glob("stylegan3_t2map_*.out"))
        if log_files:
            latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
            print(f"✅ 最新日志: {latest_log.name}")
            
            # 读取最后几行
            try:
                with open(latest_log, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        print("   最后几行:")
                        for line in lines[-5:]:
                            print(f"   {line.strip()}")
            except Exception as e:
                print(f"   日志读取失败: {e}")
        else:
            print("❌ 未找到训练日志")

def analyze_samples():
    """分析生成的样本"""
    print("\n=== T2map Sample Analysis ===\n")
    
    samples_dir = Path("outputs/t2map_samples")
    if not samples_dir.exists():
        print("❌ 样本目录不存在")
        return
    
    from PIL import Image
    import numpy as np
    
    sample_files = list(samples_dir.rglob("*.png"))
    if not sample_files:
        print("❌ 未找到样本图像")
        return
    
    print(f"分析 {len(sample_files)} 张样本图像...")
    
    # 分析图像统计信息
    sizes = []
    means = []
    stds = []
    
    for img_path in sample_files[:20]:  # 只分析前20张
        try:
            img = Image.open(img_path)
            img_array = np.array(img)
            
            sizes.append(img.size)
            means.append(np.mean(img_array))
            stds.append(np.std(img_array))
        except Exception as e:
            print(f"分析 {img_path.name} 失败: {e}")
    
    if sizes:
        print(f"图像尺寸: {sizes[0]} (所有图像)")
        print(f"平均亮度: {np.mean(means):.1f} ± {np.std(means):.1f}")
        print(f"平均对比度: {np.mean(stds):.1f} ± {np.std(stds):.1f}")

def main():
    parser = argparse.ArgumentParser(description='Check T2map StyleGAN3 training status')
    parser.add_argument('--analyze-samples', action='store_true',
                       help='Analyze generated samples')
    
    args = parser.parse_args()
    
    # 切换到工作目录
    os.chdir('/common/lidxxlab/cmrchallenge/task3/stylegan3_training')
    
    check_training_status()
    
    if args.analyze_samples:
        analyze_samples()

if __name__ == "__main__":
    main()