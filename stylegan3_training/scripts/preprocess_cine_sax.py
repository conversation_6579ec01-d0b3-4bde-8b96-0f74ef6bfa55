#!/usr/bin/env python3
import os
import h5py
import numpy as np
from pathlib import Path
import cv2
from tqdm import tqdm
import argparse
from PIL import Image

def load_reconstruction_data(h5_file_path):
    """
    从h5文件中加载reconstruction数据
    """
    try:
        with h5py.File(h5_file_path, 'r') as f:
            # 查找reconstruction数据
            for key in f.keys():
                if 'reconstruction' in key.lower() or 'recon' in key.lower():
                    return f[key][:]
                elif key == 'reconstruction':
                    return f[key][:]
            
            # 如果没找到，尝试其他可能的key
            keys = list(f.keys())
            if len(keys) > 0:
                # 假设第一个key是reconstruction数据
                data = f[keys[0]][:]
                if len(data.shape) >= 3:  # 至少3维
                    return data
                    
        return None
    except Exception as e:
        print(f"Error loading {h5_file_path}: {e}")
        return None

def resize_and_pad_image(image, target_shape):
    """
    调整图像大小到目标shape，使用pad或crop
    target_shape: (ny, nx)
    """
    current_shape = image.shape[-2:]  # 取最后两个维度
    target_ny, target_nx = target_shape
    
    # 如果当前shape已经是目标shape，直接返回
    if current_shape == target_shape:
        return image
    
    # 处理多维数据
    if len(image.shape) > 2:
        # 对每个slice/time frame分别处理
        processed_slices = []
        for i in range(image.shape[0]):
            if len(image.shape) == 4:  # [time, slice, ny, nx]
                for j in range(image.shape[1]):
                    slice_2d = image[i, j]
                    processed_slice = resize_single_image(slice_2d, target_shape)
                    processed_slices.append(processed_slice)
            else:  # [time, ny, nx] or [slice, ny, nx]
                slice_2d = image[i]
                processed_slice = resize_single_image(slice_2d, target_shape)
                processed_slices.append(processed_slice)
        
        # 重新组织数据
        if len(image.shape) == 4:
            processed_array = np.array(processed_slices).reshape(
                image.shape[0], image.shape[1], target_ny, target_nx
            )
        else:
            processed_array = np.array(processed_slices).reshape(
                image.shape[0], target_ny, target_nx
            )
        return processed_array
    else:
        return resize_single_image(image, target_shape)

def resize_single_image(image, target_shape):
    """
    调整单个2D图像的大小
    """
    current_ny, current_nx = image.shape
    target_ny, target_nx = target_shape
    
    # 计算需要pad还是crop
    if current_ny <= target_ny and current_nx <= target_nx:
        # 需要pad
        pad_y = (target_ny - current_ny) // 2
        pad_x = (target_nx - current_nx) // 2
        pad_y_extra = (target_ny - current_ny) % 2
        pad_x_extra = (target_nx - current_nx) % 2
        
        padded = np.pad(image, 
                       ((pad_y, pad_y + pad_y_extra), 
                        (pad_x, pad_x + pad_x_extra)), 
                       mode='constant', constant_values=0)
        return padded
    
    elif current_ny >= target_ny and current_nx >= target_nx:
        # 需要crop
        start_y = (current_ny - target_ny) // 2
        start_x = (current_nx - target_nx) // 2
        cropped = image[start_y:start_y + target_ny, 
                      start_x:start_x + target_nx]
        return cropped
    
    else:
        # 混合情况：一个维度pad，一个维度crop
        # 先处理y维度
        if current_ny < target_ny:
            pad_y = (target_ny - current_ny) // 2
            pad_y_extra = (target_ny - current_ny) % 2
            image = np.pad(image, ((pad_y, pad_y + pad_y_extra), (0, 0)), 
                          mode='constant', constant_values=0)
        elif current_ny > target_ny:
            start_y = (current_ny - target_ny) // 2
            image = image[start_y:start_y + target_ny, :]
        
        # 再处理x维度
        current_ny, current_nx = image.shape
        if current_nx < target_nx:
            pad_x = (target_nx - current_nx) // 2
            pad_x_extra = (target_nx - current_nx) % 2
            image = np.pad(image, ((0, 0), (pad_x, pad_x + pad_x_extra)), 
                          mode='constant', constant_values=0)
        elif current_nx > target_nx:
            start_x = (current_nx - target_nx) // 2
            image = image[:, start_x:start_x + target_nx]
        
        return image

def normalize_image(image):
    """
    归一化图像到[0, 255]范围，并转换为RGB
    """
    # 转换为实数（如果是复数）
    if np.iscomplexobj(image):
        image = np.abs(image)
    
    # 归一化到[0, 1]
    image_min = np.min(image)
    image_max = np.max(image)
    if image_max > image_min:
        image = (image - image_min) / (image_max - image_min)
    
    # 转换到[0, 255]
    image = (image * 255).astype(np.uint8)
    
    # 转换为RGB（3通道）
    if len(image.shape) == 2:
        image = np.stack([image, image, image], axis=-1)
    
    return image

def process_cine_sax_data(input_dir, output_dir, target_shape=(452, 278), force_reprocess=False):
    """
    处理cine_sax数据
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 检查是否已经处理过
    if not force_reprocess and output_path.exists():
        existing_images = list(output_path.glob('*.png'))
        if len(existing_images) > 0:
            print(f"✅ Found {len(existing_images)} existing processed images in {output_dir}")
            response = input("Do you want to reprocess? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("Skipping preprocessing - using existing images")
                return len(existing_images)
    
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 获取所有h5文件
    h5_files = list(input_path.glob('*.h5'))
    print(f"Found {len(h5_files)} cine_sax files")
    
    total_images = 0
    
    for h5_file in tqdm(h5_files, desc="Processing files"):
        # 加载reconstruction数据
        recon_data = load_reconstruction_data(h5_file)
        
        if recon_data is None:
            print(f"Skipping {h5_file.name}: no reconstruction data found")
            continue
        
        print(f"Processing {h5_file.name}: shape {recon_data.shape}")
        
        # 调整大小
        resized_data = resize_and_pad_image(recon_data, target_shape)
        
        # 提取所有2D slice作为独立图像
        if len(resized_data.shape) == 4:  # [time, slice, ny, nx]
            for t in range(resized_data.shape[0]):
                for s in range(resized_data.shape[1]):
                    image_2d = resized_data[t, s]
                    normalized_image = normalize_image(image_2d)
                    
                    # 保存为PNG - 确保是RGB格式
                    filename = f"{h5_file.stem}_t{t:02d}_s{s:02d}.png"
                    output_file = output_path / filename
                    
                    # 使用PIL保存，确保RGB格式
                    if len(normalized_image.shape) == 3:
                        pil_image = Image.fromarray(normalized_image, 'RGB')
                    else:
                        pil_image = Image.fromarray(normalized_image, 'L').convert('RGB')
                    pil_image.save(str(output_file))
                    total_images += 1
        
        elif len(resized_data.shape) == 3:  # [time, ny, nx] or [slice, ny, nx]
            for i in range(resized_data.shape[0]):
                image_2d = resized_data[i]
                normalized_image = normalize_image(image_2d)
                
                filename = f"{h5_file.stem}_frame{i:02d}.png"
                output_file = output_path / filename
                cv2.imwrite(str(output_file), normalized_image)
                total_images += 1
        
        else:  # 2D image
            normalized_image = normalize_image(resized_data)
            filename = f"{h5_file.stem}.png"
            output_file = output_path / filename
            cv2.imwrite(str(output_file), normalized_image)
            total_images += 1
    
    print(f"Processing completed! Generated {total_images} images in {output_dir}")
    return total_images

def main():
    parser = argparse.ArgumentParser(description='Preprocess cine_sax data for StyleGAN3')
    parser.add_argument('--input_dir', 
                       default='/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/data/data_category/cine_sax',
                       help='Input directory with cine_sax h5 files')
    parser.add_argument('--output_dir',
                       default='/common/lidxxlab/cmrchallenge/task3/stylegan3_training/data/cine_sax_processed',
                       help='Output directory for processed images')
    parser.add_argument('--target_shape', nargs=2, type=int, default=[512, 512],
                       help='Target shape (ny nx)')
    parser.add_argument('--force', action='store_true',
                       help='Force reprocessing even if output exists')
    
    args = parser.parse_args()
    
    print(f"Input directory: {args.input_dir}")
    print(f"Output directory: {args.output_dir}")
    print(f"Target shape: {args.target_shape}")
    
    # 处理数据
    total_images = process_cine_sax_data(
        args.input_dir, 
        args.output_dir, 
        tuple(args.target_shape),
        force_reprocess=args.force
    )
    
    print(f"\n✅ Preprocessing completed!")
    print(f"Generated {total_images} images for StyleGAN3 training")

if __name__ == "__main__":
    main()
