#!/bin/bash
#SBATCH --job-name=stylegan3_t2map
#SBATCH --partition=gpu
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=16
#SBATCH --mem=200G
#SBATCH --time=256:00:00
#SBATCH --gres=gpu:l40s:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/stylegan3_training/logs/stylegan3_t2map_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/stylegan3_training/logs/stylegan3_t2map_%j.err

# 工作目录
WORKDIR=/common/lidxxlab/cmrchallenge/task3/stylegan3_training
cd $WORKDIR

# 1) 先清理已加载模块（避免版本冲突）
module purge

# 2) 加载GCC 9.5.0（满足PyTorch要求）
module load gcc11/11.3.0

# 3) 加载CUDA Toolkit 12.1.1
module load cuda12.1/toolkit/12.1.1

# 4) 激活conda环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr

# Debug: 打印环境信息
echo "=== 计算节点环境检查 ==="
echo "节点名称: $(hostname)"
echo "Python路径: $(which python)"
echo "GCC版本: $(gcc --version | head -1)"
echo "G++版本: $(g++ --version | head -1)"
echo "CUDA可用性: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "CUDA版本: $(python -c 'import torch; print(torch.version.cuda)')"
echo "PyTorch版本: $(python -c 'import torch; print(torch.__version__)')"

# 检查CUDA工具链
echo -e "\n=== CUDA工具链检查 ==="
echo "nvcc路径: $(which nvcc)"
echo "nvcc版本:"
nvcc --version
echo "CUDA_HOME: $CUDA_HOME"

# 设置CUDA环境变量
export CUDA_HOME=$(dirname $(dirname $(which nvcc)))
export PATH=$CUDA_HOME/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH

# 设置CUDA架构（L40S GPU）
export TORCH_CUDA_ARCH_LIST="8.9"

# 设置编译器环境变量
export CC=$(which gcc)
export CXX=$(which g++)

# 清理编译缓存
echo -e "\n=== 清理编译缓存 ==="
rm -rf ~/.cache/torch_extensions

echo "=== 最终环境变量 ==="
echo "CUDA_HOME: $CUDA_HOME"
echo "TORCH_CUDA_ARCH_LIST: $TORCH_CUDA_ARCH_LIST"
echo "CC: $CC"
echo "CXX: $CXX"

# 安装依赖
echo "=== 安装StyleGAN3依赖 ==="
pip install click requests tqdm pyspng ninja imageio-ffmpeg==0.4.3

# 步骤1: 数据预处理
echo "=== Step 1: T2map Data Preprocessing ==="
python scripts/preprocess_t2map.py \
    --input_dir /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/data/data_category/T2map \
    --output_dir data/t2map_processed \
    --target_size 512 512 \
    --output_format rgb \
    --colormap viridis \
    --min_images 800

# 检查预处理结果
PROCESSED_COUNT=$(find data/t2map_processed -name "*.png" | wc -l)
echo "预处理生成图像数量: $PROCESSED_COUNT"

if [ $PROCESSED_COUNT -lt 800 ]; then
    echo "❌ 图像数量不足，无法进行训练"
    exit 1
fi

# 步骤2: 创建StyleGAN3数据集
echo "=== Step 2: Creating StyleGAN3 Dataset ==="
cd stylegan3

if [ ! -f "../data/t2map_dataset.zip" ]; then
    echo "Creating T2map dataset for StyleGAN3..."
    python dataset_tool.py \
        --source ../data/t2map_processed \
        --dest ../data/t2map_dataset.zip \
        --resolution=512x512
    
    if [ $? -ne 0 ]; then
        echo "❌ 数据集创建失败"
        exit 1
    fi
else
    echo "T2map dataset already exists"
fi

# 步骤3: StyleGAN3训练
echo "=== Step 3: StyleGAN3 T2map Training ==="

# T2map特定的训练参数
# - 较小的数据集，使用较小的batch size
# - 增加训练轮数以充分学习
# - 调整gamma值适应医学图像特征

python train.py \
    --outdir ../outputs/t2map_models \
    --cfg stylegan3-r \
    --data ../data/t2map_dataset.zip \
    --gpus 4 \
    --batch 16 \
    --gamma 6.6 \
    --mirror 1 \
    --kimg 15000 \
    --snap 100 \
    --tick 10 \
    --metrics fid50k_full \
    --seed 42 

echo "StyleGAN3 T2map训练完成！"

# 步骤4: 生成样本图像
echo "=== Step 4: Generate T2map Samples ==="
cd ..

# 查找最新的模型
LATEST_MODEL=$(find outputs/t2map_models -name "network-snapshot-*.pkl" | sort -V | tail -1)

if [ -n "$LATEST_MODEL" ] && [ -f "$LATEST_MODEL" ]; then
    echo "使用模型: $LATEST_MODEL"
    
    # 创建样本输出目录
    mkdir -p outputs/t2map_samples
    
    cd stylegan3
    
    # 生成多组样本
    echo "生成随机样本..."
    python gen_images.py \
        --outdir ../outputs/t2map_samples/random \
        --seeds 0-99 \
        --network "../$LATEST_MODEL"
    
    # 生成插值样本
    echo "生成插值样本..."
    python gen_images.py \
        --outdir ../outputs/t2map_samples/interpolation \
        --seeds 0,1,2,3 \
        --network "../$LATEST_MODEL" \
        --interpolate
    
    # 生成网格样本
    echo "生成网格样本..."
    python gen_images.py \
        --outdir ../outputs/t2map_samples/grid \
        --seeds 0-63 \
        --network "../$LATEST_MODEL" \
        --grid 8x8
    
    echo "✅ T2map样本生成完成！"
    echo "样本保存在: outputs/t2map_samples/"
    
else
    echo "❌ 未找到训练好的模型"
    echo "请检查训练是否成功完成"
fi

# 步骤5: 训练状态报告
echo "=== Step 5: Training Summary ==="
echo "训练输出目录: outputs/t2map_models/"
echo "样本输出目录: outputs/t2map_samples/"
echo "日志文件: logs/stylegan3_t2map_${SLURM_JOB_ID}.out"

# 统计信息
MODEL_COUNT=$(find outputs/t2map_models -name "*.pkl" | wc -l)
SAMPLE_COUNT=$(find outputs/t2map_samples -name "*.png" 2>/dev/null | wc -l)

echo "生成的模型数量: $MODEL_COUNT"
echo "生成的样本数量: $SAMPLE_COUNT"

if [ $MODEL_COUNT -gt 0 ]; then
    echo "✅ T2map StyleGAN3训练管道执行完成！"
else
    echo "❌ 训练可能失败，请检查日志"
fi
