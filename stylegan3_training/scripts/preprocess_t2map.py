import os
import h5py
import numpy as np
from PIL import Image
import argparse
from pathlib import Path
import logging
from tqdm import tqdm

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def normalize_image(img_array):
    """标准化图像到0-255范围"""
    # 处理复数数据
    if np.iscomplexobj(img_array):
        img_array = np.abs(img_array)
    
    # 移除NaN和inf
    img_array = np.nan_to_num(img_array, nan=0.0, posinf=0.0, neginf=0.0)
    
    # 标准化到0-255
    if img_array.max() > img_array.min():
        img_array = (img_array - img_array.min()) / (img_array.max() - img_array.min())
    else:
        img_array = np.zeros_like(img_array)
    
    return (img_array * 255).astype(np.uint8)

def convert_to_rgb(gray_array, colormap_name='viridis'):
    """将灰度图像转换为RGB格式"""
    from matplotlib import cm
    
    # 归一化到0-1
    normalized = gray_array.astype(np.float32) / 255.0
    
    # 应用热力图颜色映射
    colormap = cm.get_cmap(colormap_name)
    rgb_array = colormap(normalized)[:, :, :3]  # 去掉alpha通道
    
    # 转回0-255范围
    rgb_array = (rgb_array * 255).astype(np.uint8)
    
    return rgb_array

def resize_and_pad(img_array, target_size=(512, 512), use_rgb=True):
    """调整大小并填充到目标尺寸"""
    h, w = img_array.shape[:2]
    target_h, target_w = target_size
    
    # 计算缩放比例（保持宽高比）
    scale = min(target_h / h, target_w / w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    # 使用PIL调整大小
    if use_rgb and len(img_array.shape) == 3:
        # RGB图像
        img_pil = Image.fromarray(img_array, 'RGB')
        img_resized = img_pil.resize((new_w, new_h), Image.LANCZOS)
        img_resized = np.array(img_resized)
        
        # 创建RGB画布
        canvas = np.zeros((target_h, target_w, 3), dtype=np.uint8)
    else:
        # 灰度图像
        img_pil = Image.fromarray(img_array, 'L')
        img_resized = img_pil.resize((new_w, new_h), Image.LANCZOS)
        img_resized = np.array(img_resized)
        
        # 创建灰度画布
        canvas = np.zeros(target_size, dtype=np.uint8)
    
    start_h = (target_h - new_h) // 2
    start_w = (target_w - new_w) // 2
    canvas[start_h:start_h+new_h, start_w:start_w+new_w] = img_resized
    
    return canvas

def process_h5_file(file_path, output_dir, target_size=(512, 512), output_format='rgb', colormap='viridis'):
    """处理单个H5文件"""
    logger = logging.getLogger(__name__)
    
    try:
        with h5py.File(file_path, 'r') as f:
            # 获取重建数据
            if 'reconstruction_rss' in f:
                recon_data = f['reconstruction_rss'][:]
                logger.debug(f"找到reconstruction_rss数据，形状: {recon_data.shape}")
            elif 'reconstruction' in f:
                recon_data = f['reconstruction'][:]
                logger.debug(f"找到reconstruction数据，形状: {recon_data.shape}")
            else:
                logger.warning(f"文件 {file_path.name} 中未找到重建数据")
                logger.warning(f"可用的键: {list(f.keys())}")
                return 0
            
            # 检查数据有效性
            if np.all(recon_data == 0):
                logger.warning(f"文件 {file_path.name} 的数据全为0")
                return 0
            
            if np.isnan(recon_data).all():
                logger.warning(f"文件 {file_path.name} 的数据全为NaN")
                return 0
            
            # 获取文件名（不含扩展名）
            base_name = Path(file_path).stem
            processed_count = 0
            
            # 根据数据维度处理
            if recon_data.ndim == 4:  # (time/echo, slice, height, width) - T2map格式
                logger.debug(f"处理4D T2map数据: {recon_data.shape}")
                
                # 遍历所有时间点和切片
                for t in range(recon_data.shape[0]):  # 时间/回波维度
                    for s in range(recon_data.shape[1]):  # 切片维度
                        slice_data = recon_data[t, s]  # 形状: (height, width)
                        
                        # 跳过空切片
                        if np.all(slice_data == 0) or np.isnan(slice_data).all():
                            continue
                        
                        # 检查切片是否有足够的信号
                        if np.max(slice_data) < 1e-8:  # 阈值可调整
                            continue
                        
                        # 标准化
                        normalized = normalize_image(slice_data)
                        
                        # 转换为RGB或保持灰度
                        if output_format == 'rgb':
                            rgb_image = convert_to_rgb(normalized, colormap)
                            resized = resize_and_pad(rgb_image, target_size, use_rgb=True)
                            # 保存为RGB PNG
                            output_path = output_dir / f"{base_name}_t{t:02d}_s{s:02d}.png"
                            Image.fromarray(resized, 'RGB').save(output_path)
                        else:
                            resized = resize_and_pad(normalized, target_size, use_rgb=False)
                            # 保存为灰度PNG
                            output_path = output_dir / f"{base_name}_t{t:02d}_s{s:02d}.png"
                            Image.fromarray(resized, 'L').save(output_path)
                        
                        processed_count += 1
                        
            elif recon_data.ndim == 3:  # (slices, height, width)
                logger.debug(f"处理3D数据: {recon_data.shape}")
                
                for slice_idx in range(recon_data.shape[0]):
                    slice_data = recon_data[slice_idx]
                    
                    # 跳过空切片
                    if np.all(slice_data == 0) or np.isnan(slice_data).all():
                        continue
                    
                    # 检查切片是否有足够的信号
                    if np.max(slice_data) < 1e-8:
                        continue
                    
                    # 标准化
                    normalized = normalize_image(slice_data)
                    
                    # 转换为RGB或保持灰度
                    if output_format == 'rgb':
                        rgb_image = convert_to_rgb(normalized, colormap)
                        resized = resize_and_pad(rgb_image, target_size, use_rgb=True)
                        # 保存为RGB PNG
                        output_path = output_dir / f"{base_name}_slice_{slice_idx:03d}.png"
                        Image.fromarray(resized, 'RGB').save(output_path)
                    else:
                        resized = resize_and_pad(normalized, target_size, use_rgb=False)
                        # 保存为灰度PNG
                        output_path = output_dir / f"{base_name}_slice_{slice_idx:03d}.png"
                        Image.fromarray(resized, 'L').save(output_path)
                    
                    processed_count += 1
                    
            elif recon_data.ndim == 2:  # (height, width)
                logger.debug(f"处理2D数据: {recon_data.shape}")
                
                # 检查数据是否有足够的信号
                if np.max(recon_data) < 1e-8:
                    logger.warning(f"文件 {file_path.name} 信号太弱")
                    return 0
                
                normalized = normalize_image(recon_data)
                
                if output_format == 'rgb':
                    rgb_image = convert_to_rgb(normalized, colormap)
                    resized = resize_and_pad(rgb_image, target_size, use_rgb=True)
                    output_path = output_dir / f"{base_name}.png"
                    Image.fromarray(resized, 'RGB').save(output_path)
                else:
                    resized = resize_and_pad(normalized, target_size, use_rgb=False)
                    output_path = output_dir / f"{base_name}.png"
                    Image.fromarray(resized, 'L').save(output_path)
                
                processed_count += 1
            
            else:
                logger.warning(f"不支持的数据维度: {recon_data.ndim}, 形状: {recon_data.shape}")
                return 0
            
            if processed_count > 0:
                logger.info(f"文件 {file_path.name} 处理完成: {processed_count} 张图像")
            else:
                logger.warning(f"文件 {file_path.name} 未生成任何图像")
            
            return processed_count
            
    except Exception as e:
        logger.error(f"处理文件 {file_path} 时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 0

def main():
    parser = argparse.ArgumentParser(description='Preprocess T2map data for StyleGAN3')
    parser.add_argument('--input_dir', type=str, required=True,
                       help='Input directory containing T2map .h5 files')
    parser.add_argument('--output_dir', type=str, required=True,
                       help='Output directory for processed images')
    parser.add_argument('--target_size', type=int, nargs=2, default=[512, 512],
                       help='Target image size (height width)')
    parser.add_argument('--output_format', type=str, choices=['rgb', 'grayscale'], 
                       default='rgb', help='Output image format')
    parser.add_argument('--colormap', type=str, default='viridis',
                       choices=['viridis', 'plasma', 'inferno', 'magma', 'hot', 'gray'],
                       help='Colormap for RGB conversion')
    parser.add_argument('--min_images', type=int, default=1000,
                       help='Minimum number of images for training')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找所有T2map文件
    input_dir = Path(args.input_dir)
    h5_files = list(input_dir.rglob("*T2map*.h5"))
    
    logger.info(f"Found {len(h5_files)} T2map files")
    logger.info(f"Target size: {args.target_size}")
    logger.info(f"Output format: {args.output_format}")
    if args.output_format == 'rgb':
        logger.info(f"Colormap: {args.colormap}")
    
    if not h5_files:
        logger.error("No T2map files found!")
        return
    
    # 处理文件
    total_images = 0
    failed_files = []
    
    for file_path in tqdm(h5_files, desc="Processing T2map files"):
        count = process_h5_file(file_path, output_dir, tuple(args.target_size), args.output_format, args.colormap)
        if count > 0:
            total_images += count
            logger.info(f"Processed {file_path.name}: {count} images")
        else:
            failed_files.append(file_path)
    
    logger.info(f"\n=== Processing Summary ===")
    logger.info(f"Total files processed: {len(h5_files) - len(failed_files)}")
    logger.info(f"Failed files: {len(failed_files)}")
    logger.info(f"Total images generated: {total_images}")
    logger.info(f"Output format: {args.output_format}")
    
    if failed_files:
        logger.warning("Failed files:")
        for f in failed_files:
            logger.warning(f"  {f}")
    
    # 检查是否有足够的图像进行训练
    if total_images < args.min_images:
        logger.warning(f"Only {total_images} images generated, less than minimum {args.min_images}")
        logger.warning("Consider lowering --min_images or checking data quality")
    else:
        logger.info(f"✅ Generated {total_images} {args.output_format} images, ready for StyleGAN3 training!")

if __name__ == "__main__":
    main()
