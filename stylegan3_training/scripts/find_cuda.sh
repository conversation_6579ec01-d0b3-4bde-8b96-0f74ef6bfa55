#!/bin/bash

echo "=== 查找CUDA安装位置 ==="

# 查找nvcc
echo "1. 查找nvcc:"
find /usr -name "nvcc" 2>/dev/null
find /opt -name "nvcc" 2>/dev/null
which nvcc 2>/dev/null || echo "nvcc not in PATH"

# 查找CUDA目录
echo -e "\n2. 查找CUDA目录:"
ls -la /usr/local/ | grep cuda
ls -la /opt/ | grep cuda 2>/dev/null

# 查找cuda_runtime_api.h
echo -e "\n3. 查找CUDA头文件:"
find /usr -name "cuda_runtime_api.h" 2>/dev/null
find /opt -name "cuda_runtime_api.h" 2>/dev/null

# 检查conda环境中的CUDA
echo -e "\n4. 检查conda环境中的CUDA:"
find $CONDA_PREFIX -name "nvcc" 2>/dev/null
find $CONDA_PREFIX -name "cuda_runtime_api.h" 2>/dev/null

echo -e "\n5. 当前环境变量:"
echo "CUDA_HOME: $CUDA_HOME"
echo "PATH: $PATH"