#!/usr/bin/env python3
import os
from pathlib import Path
import glob

def check_training_status():
    """检查训练状态和可用模型"""
    
    # 检查预处理数据
    data_dir = Path("data/cine_sax_processed")
    if data_dir.exists():
        images = list(data_dir.glob("*.png"))
        print(f"✅ Preprocessed images: {len(images)}")
    else:
        print("❌ No preprocessed data found")
        return
    
    # 检查数据集
    dataset_file = Path("data/cine_sax_dataset.zip")
    if dataset_file.exists():
        print(f"✅ Dataset created: {dataset_file}")
    else:
        print("❌ Dataset not created")
    
    # 检查训练输出
    output_dir = Path("outputs/cine_sax_models")
    if output_dir.exists():
        pkl_files = list(output_dir.glob("*.pkl"))
        if pkl_files:
            latest_model = max(pkl_files, key=os.path.getctime)
            print(f"✅ Latest model: {latest_model}")
            print(f"✅ Total models: {len(pkl_files)}")
            
            # 返回最新模型路径供其他脚本使用
            return str(latest_model)
        else:
            print("❌ No model files found")
    else:
        print("❌ Output directory not found")
    
    return None

if __name__ == "__main__":
    latest_model = check_training_status()
    if latest_model:
        print(f"\nTo generate images, run:")
        print(f"cd stylegan3")
        print(f"python gen_images.py --outdir ../outputs/samples --seeds 0-63 --network {latest_model}")