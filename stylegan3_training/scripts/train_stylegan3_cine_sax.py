#!/usr/bin/env python3
import os
import sys
import subprocess
from pathlib import Path

def prepare_dataset(data_dir, dataset_name):
    """
    准备StyleGAN3数据集
    """
    stylegan3_dir = Path(__file__).parent / "stylegan3"
    
    # 创建数据集
    cmd = [
        sys.executable, 
        str(stylegan3_dir / "dataset_tool.py"),
        "--source", str(data_dir),
        "--dest", f"data/{dataset_name}.zip",
        "--resolution", "512x512",  # StyleGAN3推荐的分辨率
        "--transform", "center-crop-wide"
    ]
    
    print(f"Creating dataset: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=stylegan3_dir)
    
    if result.returncode != 0:
        print("Dataset creation failed!")
        return False
    
    return True

def train_stylegan3(dataset_path, output_dir, config='stylegan3-r'):
    """
    训练StyleGAN3模型
    """
    stylegan3_dir = Path(__file__).parent / "stylegan3"
    
    # 训练命令
    cmd = [
        sys.executable,
        str(stylegan3_dir / "train.py"),
        "--outdir", str(output_dir),
        "--cfg", config,
        "--data", str(dataset_path),
        "--gpus", "4",  # 使用4个GPU
        "--batch", "32",  # 批大小
        "--gamma", "8.2",  # R1正则化参数
        "--mirror", "1",  # 数据增强
        "--kimg", "5000",  # 训练5000k图像
        "--snap", "50",  # 每50个tick保存一次
        "--metrics", "fid50k_full",  # 评估指标
        "--resume", "ffhq512"  # 从预训练模型开始
    ]
    
    print(f"Training command: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=stylegan3_dir)
    
    return result.returncode == 0

def main():
    # 配置路径
    data_dir = "/common/lidxxlab/cmrchallenge/task3/stylegan3_training/data/cine_sax_processed"
    dataset_name = "cine_sax_dataset"
    output_dir = "/common/lidxxlab/cmrchallenge/task3/stylegan3_training/outputs/cine_sax_models"
    
    print("🚀 Starting StyleGAN3 training pipeline for cine_sax")
    
    # 检查数据目录
    if not os.path.exists(data_dir):
        print(f"Error: Data directory {data_dir} does not exist!")
        print("Please run preprocess_cine_sax.py first")
        return
    
    # 检查图像数量
    image_files = list(Path(data_dir).glob("*.png"))
    print(f"Found {len(image_files)} images for training")
    
    if len(image_files) < 100:
        print("Warning: Very few images found. StyleGAN3 typically needs thousands of images.")
    
    # 准备数据集
    print("\n📦 Preparing dataset...")
    dataset_path = f"data/{dataset_name}.zip"
    if not prepare_dataset(data_dir, dataset_name):
        print("Dataset preparation failed!")
        return
    
    # 开始训练
    print("\n🎯 Starting StyleGAN3 training...")
    if train_stylegan3(dataset_path, output_dir):
        print("✅ Training completed successfully!")
    else:
        print("❌ Training failed!")

if __name__ == "__main__":
    main()