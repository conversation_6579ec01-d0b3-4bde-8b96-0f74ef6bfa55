#!/usr/bin/env python3
"""
调试T2map H5文件结构
"""

import h5py
import numpy as np
from pathlib import Path
import argparse

def inspect_h5_file(file_path):
    """检查单个H5文件的结构"""
    print(f"\n=== 检查文件: {file_path.name} ===")
    
    try:
        with h5py.File(file_path, 'r') as f:
            print("文件中的所有键:")
            
            def print_structure(name, obj):
                if isinstance(obj, h5py.Dataset):
                    print(f"  数据集: {name} - 形状: {obj.shape}, 类型: {obj.dtype}")
                    if obj.size > 0:
                        print(f"    数值范围: {np.min(obj[()])} ~ {np.max(obj[()])}")
                elif isinstance(obj, h5py.Group):
                    print(f"  组: {name}")
            
            f.visititems(print_structure)
            
            # 检查常见的重建数据字段
            possible_keys = [
                'reconstruction_rss', 'reconstruction', 'recon', 'image',
                'T2map', 't2map', 'map', 'data', 'img'
            ]
            
            print("\n检查可能的重建数据字段:")
            found_keys = []
            for key in possible_keys:
                if key in f:
                    found_keys.append(key)
                    data = f[key]
                    print(f"  ✅ 找到 '{key}': 形状 {data.shape}, 类型 {data.dtype}")
                    
                    # 检查数据是否有效
                    sample_data = data[()]
                    if np.all(sample_data == 0):
                        print(f"    ⚠️  数据全为0")
                    elif np.isnan(sample_data).all():
                        print(f"    ⚠️  数据全为NaN")
                    else:
                        print(f"    ✅ 数据有效，范围: {np.nanmin(sample_data)} ~ {np.nanmax(sample_data)}")
                else:
                    print(f"  ❌ 未找到 '{key}'")
            
            return found_keys
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return []

def main():
    parser = argparse.ArgumentParser(description='Debug T2map H5 file structure')
    parser.add_argument('--input_dir', type=str, 
                       default='/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/data/data_category/T2map',
                       help='Input directory containing T2map .h5 files')
    parser.add_argument('--max_files', type=int, default=5,
                       help='Maximum number of files to inspect')
    
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    h5_files = list(input_dir.glob("*T2map*.h5"))
    
    print(f"找到 {len(h5_files)} 个T2map文件")
    print(f"检查前 {min(args.max_files, len(h5_files))} 个文件...")
    
    all_found_keys = set()
    
    for i, file_path in enumerate(h5_files[:args.max_files]):
        found_keys = inspect_h5_file(file_path)
        all_found_keys.update(found_keys)
    
    print(f"\n=== 总结 ===")
    print(f"所有文件中找到的数据字段: {list(all_found_keys)}")
    
    if not all_found_keys:
        print("❌ 未在任何文件中找到重建数据")
        print("建议检查文件格式或联系数据提供方")
    else:
        print("✅ 找到可用的数据字段，可以更新预处理脚本")

if __name__ == "__main__":
    main()