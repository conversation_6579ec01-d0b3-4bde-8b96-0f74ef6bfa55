2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_setup.py:_flush():67] Configure stats pid to 1713557
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_setup.py:_flush():67] Loading settings from /common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/wandb/settings
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/logs/debug.log
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/logs/debug-internal.log
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_init.py:init():781] calling init triggers
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_init.py:init():809] starting backend
2025-08-12 18:23:14,667 INFO    MainThread:1713557 [wandb_init.py:init():813] sending inform_init request
2025-08-12 18:23:14,685 INFO    MainThread:1713557 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-08-12 18:23:14,685 INFO    MainThread:1713557 [wandb_init.py:init():823] backend started and connected
2025-08-12 18:23:14,685 DEBUG   MainThread:1713557 [config_util.py:dict_from_config_file():70] no default config file found in config-defaults.yaml
2025-08-12 18:23:14,688 INFO    MainThread:1713557 [wandb_init.py:init():915] updated telemetry
2025-08-12 18:23:14,688 INFO    MainThread:1713557 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-08-12 18:23:15,083 INFO    MainThread:1713557 [wandb_init.py:init():1014] starting run threads in backend
2025-08-12 18:23:15,090 DEBUG   MainThread:1713557 [wandb_run.py:_on_start():2486] Saving list of pip packages installed into the current environment
2025-08-12 18:23:15,175 INFO    MainThread:1713557 [wandb_run.py:_console_start():2454] atexit reg
2025-08-12 18:23:15,175 INFO    MainThread:1713557 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-12 18:23:15,175 INFO    MainThread:1713557 [wandb_run.py:_redirect():2371] Wrapping output streams.
2025-08-12 18:23:15,175 INFO    MainThread:1713557 [wandb_run.py:_redirect():2394] Redirects installed.
2025-08-12 18:23:15,177 INFO    MainThread:1713557 [wandb_init.py:init():1056] run started, returning control to user process
2025-08-12 18:23:51,337 INFO    MsgRouterThr:1713557 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
