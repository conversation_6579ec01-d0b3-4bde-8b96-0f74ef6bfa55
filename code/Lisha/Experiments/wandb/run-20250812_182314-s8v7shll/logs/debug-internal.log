{"time":"2025-08-12T18:23:14.691568072-07:00","level":"INFO","msg":"stream: starting","core version":"0.19.9","symlink path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/logs/debug-core.log"}
{"time":"2025-08-12T18:23:14.691635709-07:00","level":"DEBUG","msg":"monitor: sampling interval: 15s"}
{"time":"2025-08-12T18:23:14.804820733-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:14.881063397-07:00","level":"INFO","msg":"created new stream","id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.881084135-07:00","level":"INFO","msg":"stream: started","id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.881098234-07:00","level":"INFO","msg":"writer: Do: started","stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.881118799-07:00","level":"INFO","msg":"handler: started","stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.88116987-07:00","level":"INFO","msg":"sender: started","stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.881567857-07:00","level":"DEBUG","msg":"handling record","record":{"Header":{}}}
{"time":"2025-08-12T18:23:14.88158597-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Header); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:14.881629773-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Header":{}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.881953007-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"s8v7shll","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:23:14.881967457-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(mailbox_slot:\"699nmswomylk\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:14.881977327-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"s8v7shll","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"699nmswomylk","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.882799941-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.882832192-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"s8v7shll","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"699nmswomylk","always_send":true,"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.882866138-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":1,"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.882871969-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":2,"RecordType":{"Run":{"run_id":"s8v7shll","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"699nmswomylk","always_send":true,"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:14.882990462-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:15.082729809-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"RunResult":{"run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","storage_id":"UnVuOnYxOnM4djdzaGxsOmNtcjIwMjVfdGFzazM6bGlzaGEtemVuZy1jZWRhcnMtc2luYWk=","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}},"control":{"mailbox_slot":"699nmswomylk","always_send":true,"connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:15.08428101-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}}}
{"time":"2025-08-12T18:23:15.084299633-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_RunStart); Control(local:true mailbox_slot:\"5dqft0aurtam\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.084308067-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"5dqft0aurtam","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.084502681-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"5dqft0aurtam","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.084557217-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"5dqft0aurtam","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.084570386-07:00","level":"DEBUG","msg":"filestream: start","path":""}
{"time":"2025-08-12T18:23:15.084585692-07:00","level":"DEBUG","msg":"filestream: open","path":"files/lisha-zeng-cedars-sinai/cmr2025_task3/s8v7shll/file_stream"}
{"time":"2025-08-12T18:23:15.089364425-07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-12T18:23:15.089377014-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":null}},"control":{"local":true,"mailbox_slot":"5dqft0aurtam","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:15.089430618-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.089471415-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":3,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.118456795-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_Metadata); Control(<nil>)","buffer":0}
{"time":"2025-08-12T18:23:15.118473274-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"Metadata":{"cpu_count":96,"cpu_count_logical":96,"gpu_type":"NVIDIA H100 80GB HBM3","gpu_count":4,"disk":{"/":{"total":942552190976,"used":18732859392}},"memory":{"total":2164152020992},"cpu":{"count":96,"count_logical":96},"gpu_nvidia":[{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"}],"slurm":{"cluster_name":"slurm-compbio","conf":"/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf","cpu_bind":"quiet,mask_cpu:0x00000000000F000000000017","cpu_bind_list":"0x00000000000F000000000017","cpu_bind_type":"mask_cpu:","cpu_bind_verbose":"quiet","cpus_on_node":"8","cpus_per_task":"8","distribution":"cyclic","gpus_on_node":"4","gtids":"0","job_account":"user","job_cpus_per_node":"8","job_end_time":"**********","job_gid":"23023","job_gpus":"0,1,2,3","job_id":"2155084","job_name":"cmr_baselineXL_task3","job_nodelist":"esplhpc-cp088","job_num_nodes":"1","job_partition":"gpu","job_qos":"normal","job_start_time":"**********","job_uid":"1235884","job_user":"zengl2","jobid":"2155084","launch_node_ipaddr":"************","localid":"0","mem_per_node":"131072","nnodes":"1","nodeid":"0","nodelist":"esplhpc-cp088","nprocs":"1","ntasks":"1","prio_process":"0","procid":"0","srun_comm_host":"************","srun_comm_port":"42453","step_gpus":"0,1,2,3","step_id":"0","step_launcher_port":"42453","step_nodelist":"esplhpc-cp088","step_num_nodes":"1","step_num_tasks":"1","step_tasks_per_node":"1","stepid":"0","submit_dir":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3","submit_host":"esplhpccompbio-lv02","task_pid":"1713557","tasks_per_node":"1","topology_addr":"esplhpc-cp088","topology_addr_pattern":"node","tres_per_task":"cpu:8","umask":"0022"},"cuda_version":"12.4"}}}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.121888898-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.121909574-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":4,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.139696157-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["wandb-metadata.json"]}
{"time":"2025-08-12T18:23:15.13978449-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:15.17490046-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}}}
{"time":"2025-08-12T18:23:15.174928471-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_PythonPackages); Control(local:true connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.174938782-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}},"control":{"local":true,"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.176479541-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.176503695-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":5,"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/wandb-metadata.json, Name: wandb-metadata.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012315Z&X-Goog-Expires=86399&X-Goog-Signature=62bc4bc1881c72f6c32e687ee0a6ad3c3fc84fc361433832e3fd48e2a82fcc4459fccf0ee6ea8c4b2359435438b835f62753e5ee9e97b65c475f89502fd9cb3622ee046976ecb03472c2401f40bcd0faf2342c37694a14f6be83e380676876bef3b1d10bc00f986c6b9b2e4e178a71af629252eb3481cd6dbd3dc798f885a429ece384782a6d69f8451190e05992ac7264674ca4dce5ebd3f3c000c495cf7b201ec8ed2312ee92086c4273fb37e533090f9eb1372fcb2113f197b674aba71aeb4e91dbdbf8ba88f02b750674c5e197d82677886486759e8fd031edf09bb3aedd99283d06c2582b01621ce65089e54404a7a62a3264f1e03dd4558ec9d08d7a09&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/wandb-metadata.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012315Z&X-Goog-Expires=86399&X-Goog-Signature=62bc4bc1881c72f6c32e687ee0a6ad3c3fc84fc361433832e3fd48e2a82fcc4459fccf0ee6ea8c4b2359435438b835f62753e5ee9e97b65c475f89502fd9cb3622ee046976ecb03472c2401f40bcd0faf2342c37694a14f6be83e380676876bef3b1d10bc00f986c6b9b2e4e178a71af629252eb3481cd6dbd3dc798f885a429ece384782a6d69f8451190e05992ac7264674ca4dce5ebd3f3c000c495cf7b201ec8ed2312ee92086c4273fb37e533090f9eb1372fcb2113f197b674aba71aeb4e91dbdbf8ba88f02b750674c5e197d82677886486759e8fd031edf09bb3aedd99283d06c2582b01621ce65089e54404a7a62a3264f1e03dd4558ec9d08d7a09&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012315Z&X-Goog-Expires=86399&X-Goog-Signature=62bc4bc1881c72f6c32e687ee0a6ad3c3fc84fc361433832e3fd48e2a82fcc4459fccf0ee6ea8c4b2359435438b835f62753e5ee9e97b65c475f89502fd9cb3622ee046976ecb03472c2401f40bcd0faf2342c37694a14f6be83e380676876bef3b1d10bc00f986c6b9b2e4e178a71af629252eb3481cd6dbd3dc798f885a429ece384782a6d69f8451190e05992ac7264674ca4dce5ebd3f3c000c495cf7b201ec8ed2312ee92086c4273fb37e533090f9eb1372fcb2113f197b674aba71aeb4e91dbdbf8ba88f02b750674c5e197d82677886486759e8fd031edf09bb3aedd99283d06c2582b01621ce65089e54404a7a62a3264f1e03dd4558ec9d08d7a09&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"7k7d8bh07qno\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.215793644-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"NetworkStatus":{}}}}}
{"time":"2025-08-12T18:23:15.215798273-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_NetworkStatus); Control(local:true mailbox_slot:\"boo45yhyltzt\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.21581409-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:23:15.215826148-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:15.21585684-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:23:15.215861125-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"l5xpfl4culpb\" connection_id:\"127.0.0.1:53322\")","buffer":2}
{"time":"2025-08-12T18:23:15.215850151-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"7k7d8bh07qno","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215870559-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:23:15.215872586-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"boo45yhyltzt","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215877215-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:53322\")","buffer":2}
{"time":"2025-08-12T18:23:15.215877794-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215889984-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"l5xpfl4culpb","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215899754-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215925919-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}}}
{"time":"2025-08-12T18:23:15.215931099-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.215906862-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"l5xpfl4culpb","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:15.215939387-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"7k7d8bh07qno","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.21595505-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215956405-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"boo45yhyltzt","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215943169-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:23:15.215967505-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.215962083-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215972602-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215978877-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215978367-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215987743-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"7k7d8bh07qno","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215989671-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.215975967-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}}}
{"time":"2025-08-12T18:23:15.215998277-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.216009004-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:23:15.216015057-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.216023391-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:15.216027928-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:23:15.216036603-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:15.21603926-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.21604421-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.216050709-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.216057566-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.216060669-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.21606573-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.22652916-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["requirements.txt"]}
{"time":"2025-08-12T18:23:15.226577603-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/requirements.txt, Name: requirements.txt, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012315Z&X-Goog-Expires=86399&X-Goog-Signature=41783a7998c11761591cb55f2e45463d7b94c95a283861af9038e3891272c19ccb4b405d9cb6d3d8d086780be68ca56b5f86a82c8431cd4bab0368dbde23b35ada8e5dcf5de061292780c159c8492fdda468fa3fc36f1ab52813fded15503331bfa3cf565808016d49aaa201afbc8f73ac2181f772e65934a908e063607be0b018f079b37f39c0facc6b54ced0eb822bb136fe4ae91b188d113301b8ab29a3a3c1f3222bca8da8825736a84cc13b9a96c6e3f44e6c0fb058078e52e3e18ec1f51ffc49ef33675792fc419cc3c05a8ae9841f165b620370a5bd6759292e237f94f9b6295900bdbe375b5317255c1d7c06fea15cedd7795bcc7e54e6a356ed2bf3&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/requirements.txt","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012315Z&X-Goog-Expires=86399&X-Goog-Signature=41783a7998c11761591cb55f2e45463d7b94c95a283861af9038e3891272c19ccb4b405d9cb6d3d8d086780be68ca56b5f86a82c8431cd4bab0368dbde23b35ada8e5dcf5de061292780c159c8492fdda468fa3fc36f1ab52813fded15503331bfa3cf565808016d49aaa201afbc8f73ac2181f772e65934a908e063607be0b018f079b37f39c0facc6b54ced0eb822bb136fe4ae91b188d113301b8ab29a3a3c1f3222bca8da8825736a84cc13b9a96c6e3f44e6c0fb058078e52e3e18ec1f51ffc49ef33675792fc419cc3c05a8ae9841f165b620370a5bd6759292e237f94f9b6295900bdbe375b5317255c1d7c06fea15cedd7795bcc7e54e6a356ed2bf3&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012315Z&X-Goog-Expires=86399&X-Goog-Signature=41783a7998c11761591cb55f2e45463d7b94c95a283861af9038e3891272c19ccb4b405d9cb6d3d8d086780be68ca56b5f86a82c8431cd4bab0368dbde23b35ada8e5dcf5de061292780c159c8492fdda468fa3fc36f1ab52813fded15503331bfa3cf565808016d49aaa201afbc8f73ac2181f772e65934a908e063607be0b018f079b37f39c0facc6b54ced0eb822bb136fe4ae91b188d113301b8ab29a3a3c1f3222bca8da8825736a84cc13b9a96c6e3f44e6c0fb058078e52e3e18ec1f51ffc49ef33675792fc419cc3c05a8ae9841f165b620370a5bd6759292e237f94f9b6295900bdbe375b5317255c1d7c06fea15cedd7795bcc7e54e6a356ed2bf3&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:15.*********-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"boo45yhyltzt","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.407278435-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":6,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.407307708-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:23:15.407342868-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:15.407309378-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"7k7d8bh07qno","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:15.45078309-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-metadata.json"}}
{"time":"2025-08-12T18:23:15.450867744-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"uploaded\":[\"wandb-metadata.json\"]}"}
{"time":"2025-08-12T18:23:15.450885595-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/s8v7shll/file_stream"}
{"time":"2025-08-12T18:23:15.48701438-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"requirements.txt"}}
{"time":"2025-08-12T18:23:15.504476498-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":7,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.504505895-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":8,"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.504510847-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":9,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.504555817-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":10,"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.504559577-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":11,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.504575233-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":12,"RecordType":{"Run":{"run_id":"s8v7shll","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755048194,"nanos":685854000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.504625098-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:15.523369817-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523101000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/s8v7shll/config.yaml"}}}
{"time":"2025-08-12T18:23:15.523386223-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.523394855-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523101000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/s8v7shll/config.yaml"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.52340138-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523101000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/s8v7shll/config.yaml"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.539663817-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:23:15.563680665-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523197000},"line":"\n"}}}
{"time":"2025-08-12T18:23:15.563692598-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.563701815-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":524605000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\n"}}}
{"time":"2025-08-12T18:23:15.56370627-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:15.563719398-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":536222000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}}}
{"time":"2025-08-12T18:23:15.563723761-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:15.563728205-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523197000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.563733062-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":524605000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.563736185-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":536222000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.563740334-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523197000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.563743368-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":524605000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.563746008-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":536222000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.598173887-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":13,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523101000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/s8v7shll/config.yaml"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.598214663-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":14,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":523197000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.598220063-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":15,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":524605000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.598262728-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":16,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":536222000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:15.602755524-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:23:25.175956099-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:23:25.176133492-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"25ispir355fs\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:25.176144999-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"25ispir355fs","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:25.176167538-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"25ispir355fs","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:30.090193167-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:23:30.090220971-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048210,"nanos":89942268},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.975"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.99642857142857"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.507"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.215285714285717"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.371"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195857142857143"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.711"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244428571428571"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.090286538-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048210,"nanos":89942268},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.975"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.99642857142857"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.507"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.215285714285717"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.371"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195857142857143"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.711"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244428571428571"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.090320511-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":17,"RecordType":{"Stats":{"timestamp":{"seconds":1755048210,"nanos":89942268},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.975"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.99642857142857"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.507"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.215285714285717"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.371"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195857142857143"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.711"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244428571428571"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.090345069-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:23:14.685854Z","Record":{"timestamp":{"seconds":1755048210,"nanos":89942268},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.975"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.99642857142857"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.507"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.215285714285717"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.371"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195857142857143"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.711"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244428571428571"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}}}
{"time":"2025-08-12T18:23:30.107774049-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:23:30.107794968-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048210,"nanos":107752472},"item":[{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"5.031579e+06"},{"key":"proc.memory.rssMB","value_json":"10903.31640625"},{"key":"cpu","value_json":"0.9817156083625669"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"network.recv","value_json":"1.99612248e+08"},{"key":"memory_percent","value_json":"2.3828509559306337"},{"key":"proc.memory.availableMB","value_json":"2.00366689453125e+06"},{"key":"proc.memory.percent","value_json":"0.5282880219643434"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.107814326-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048210,"nanos":107752472},"item":[{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"5.031579e+06"},{"key":"proc.memory.rssMB","value_json":"10903.31640625"},{"key":"cpu","value_json":"0.9817156083625669"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"network.recv","value_json":"1.99612248e+08"},{"key":"memory_percent","value_json":"2.3828509559306337"},{"key":"proc.memory.availableMB","value_json":"2.00366689453125e+06"},{"key":"proc.memory.percent","value_json":"0.5282880219643434"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.107828758-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":18,"RecordType":{"Stats":{"timestamp":{"seconds":1755048210,"nanos":107752472},"item":[{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"5.031579e+06"},{"key":"proc.memory.rssMB","value_json":"10903.31640625"},{"key":"cpu","value_json":"0.9817156083625669"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"network.recv","value_json":"1.99612248e+08"},{"key":"memory_percent","value_json":"2.3828509559306337"},{"key":"proc.memory.availableMB","value_json":"2.00366689453125e+06"},{"key":"proc.memory.percent","value_json":"0.5282880219643434"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.107835197-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:23:14.685854Z","Record":{"timestamp":{"seconds":1755048210,"nanos":107752472},"item":[{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"5.031579e+06"},{"key":"proc.memory.rssMB","value_json":"10903.31640625"},{"key":"cpu","value_json":"0.9817156083625669"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"network.recv","value_json":"1.99612248e+08"},{"key":"memory_percent","value_json":"2.3828509559306337"},{"key":"proc.memory.availableMB","value_json":"2.00366689453125e+06"},{"key":"proc.memory.percent","value_json":"0.5282880219643434"}]}}}
{"time":"2025-08-12T18:23:30.175224325-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:23:30.175239017-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"s4trtgfk2jy6\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:30.175247638-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"s4trtgfk2jy6","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.175253607-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"s4trtgfk2jy6","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.175261364-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"s4trtgfk2jy6","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:30.175293625-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:30.324848839-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"s4trtgfk2jy6","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:30.451912193-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":0,\"content\":[\"{\\\"ts\\\":\\\"2025-08-13T01:23:15.598191\\\",\\\"content\\\":\\\"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/s8v7shll/config.yaml\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:15.598222\\\",\\\"content\\\":\\\"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:15.598267\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\\\"}\"]},\"wandb-events.jsonl\":{\"offset\":0,\"content\":[\"{\\\"system.gpu.2.powerWatts\\\":71.371,\\\"system.gpu.3.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.1.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.2.temp\\\":28,\\\"system.gpu.1.memory\\\":0,\\\"system.gpu.1.powerWatts\\\":71.507,\\\"system.gpu.2.gpu\\\":0,\\\"system.gpu.2.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.2.correctedMemoryErrors\\\":0,\\\"system.gpu.3.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.0.gpu\\\":0,\\\"system.gpu.0.correctedMemoryErrors\\\":0,\\\"system.gpu.0.memoryAllocated\\\":1.8354810627889013,\\\"system.gpu.1.temp\\\":27,\\\"system.gpu.1.smClock\\\":345,\\\"system.gpu.1.uncorrectedMemoryErrors\\\":0,\\\"_runtime\\\":15.404088268,\\\"system.gpu.0.memory\\\":0,\\\"system.gpu.2.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.0.powerPercent\\\":15.99642857142857,\\\"system.gpu.1.correctedMemoryErrors\\\":0,\\\"system.gpu.1.powerPercent\\\":10.215285714285717,\\\"system.gpu.3.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.3.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.3.smClock\\\":345,\\\"system.gpu.0.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.1.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.3.gpu\\\":0,\\\"system.gpu.3.powerWatts\\\":71.711,\\\"system.gpu.3.powerPercent\\\":10.244428571428571,\\\"system.gpu.3.memoryClock\\\":2619,\\\"system.gpu.3.correctedMemoryErrors\\\":0,\\\"system.gpu.0.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.2.memory\\\":0,\\\"system.gpu.1.gpu\\\":0,\\\"system.gpu.1.memoryClock\\\":2619,\\\"system.gpu.2.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.2.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.2.powerPercent\\\":10.195857142857143,\\\"system.gpu.2.memoryClock\\\":2619,\\\"_timestamp\\\":1.7550482100899422e+09,\\\"system.gpu.0.smClock\\\":1980,\\\"system.gpu.3.memory\\\":0,\\\"system.gpu.0.temp\\\":34,\\\"system.gpu.0.powerWatts\\\":111.975,\\\"system.gpu.0.memoryClock\\\":2619,\\\"system.gpu.1.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.2.smClock\\\":345,\\\"system.gpu.3.temp\\\":33,\\\"_wandb\\\":true,\\\"system.gpu.0.memoryAllocatedBytes\\\":1569718272}\",\"{\\\"_timestamp\\\":1.7550482101077526e+09,\\\"_runtime\\\":15.421898472,\\\"system.network.sent\\\":5.031579e+06,\\\"system.proc.cpu.threads\\\":15,\\\"_wandb\\\":true,\\\"system.disk./.usageGB\\\":17.446334838867188,\\\"system.proc.memory.rssMB\\\":10903.31640625,\\\"system.cpu\\\":0.9817156083625669,\\\"system.disk./.usagePercent\\\":1.9874612325289254,\\\"system.network.recv\\\":1.99612248e+08,\\\"system.memory_percent\\\":2.3828509559306337,\\\"system.proc.memory.availableMB\\\":2.00366689453125e+06,\\\"system.proc.memory.percent\\\":0.5282880219643434}\"]}},\"uploaded\":[\"requirements.txt\"]}"}
{"time":"2025-08-12T18:23:30.451969734-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/s8v7shll/file_stream"}
{"time":"2025-08-12T18:23:30.584506976-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:23:35.175887625-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:23:35.175904864-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"3dkuqyxpe7qw\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:35.17591136-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"3dkuqyxpe7qw","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:35.175919896-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"3dkuqyxpe7qw","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:45.094128225-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:23:45.094456101-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048225,"nanos":93929896},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.163"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.023285714285716"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.543"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.220428571428572"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.366"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195142857142857"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.709"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244142857142858"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.094499148-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048225,"nanos":93929896},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.163"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.023285714285716"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.543"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.220428571428572"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.366"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195142857142857"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.709"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244142857142858"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.094513981-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":19,"RecordType":{"Stats":{"timestamp":{"seconds":1755048225,"nanos":93929896},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.163"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.023285714285716"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.543"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.220428571428572"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.366"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195142857142857"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.709"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244142857142858"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.094526837-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:23:14.685854Z","Record":{"timestamp":{"seconds":1755048225,"nanos":93929896},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.163"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.023285714285716"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.543"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.220428571428572"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.366"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.195142857142857"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.709"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.244142857142858"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}}}
{"time":"2025-08-12T18:23:45.104543221-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:23:45.104569006-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048225,"nanos":104516863},"item":[{"key":"network.recv","value_json":"1.058999269e+09"},{"key":"proc.memory.availableMB","value_json":"2.00196289453125e+06"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"1.3053195e+07"},{"key":"memory_percent","value_json":"2.465414004675286"},{"key":"proc.memory.rssMB","value_json":"11722.5859375"},{"key":"proc.memory.percent","value_json":"0.567983309525807"},{"key":"cpu","value_json":"0.9971090307092721"},{"key":"proc.cpu.threads","value_json":"15"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.10459685-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755048225,"nanos":104516863},"item":[{"key":"network.recv","value_json":"1.058999269e+09"},{"key":"proc.memory.availableMB","value_json":"2.00196289453125e+06"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"1.3053195e+07"},{"key":"memory_percent","value_json":"2.465414004675286"},{"key":"proc.memory.rssMB","value_json":"11722.5859375"},{"key":"proc.memory.percent","value_json":"0.567983309525807"},{"key":"cpu","value_json":"0.9971090307092721"},{"key":"proc.cpu.threads","value_json":"15"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.104607395-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":20,"RecordType":{"Stats":{"timestamp":{"seconds":1755048225,"nanos":104516863},"item":[{"key":"network.recv","value_json":"1.058999269e+09"},{"key":"proc.memory.availableMB","value_json":"2.00196289453125e+06"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"1.3053195e+07"},{"key":"memory_percent","value_json":"2.465414004675286"},{"key":"proc.memory.rssMB","value_json":"11722.5859375"},{"key":"proc.memory.percent","value_json":"0.567983309525807"},{"key":"cpu","value_json":"0.9971090307092721"},{"key":"proc.cpu.threads","value_json":"15"}]}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.104613942-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:23:14.685854Z","Record":{"timestamp":{"seconds":1755048225,"nanos":104516863},"item":[{"key":"network.recv","value_json":"1.058999269e+09"},{"key":"proc.memory.availableMB","value_json":"2.00196289453125e+06"},{"key":"disk./.usagePercent","value_json":"1.9874612325289254"},{"key":"disk./.usageGB","value_json":"17.446334838867188"},{"key":"network.sent","value_json":"1.3053195e+07"},{"key":"memory_percent","value_json":"2.465414004675286"},{"key":"proc.memory.rssMB","value_json":"11722.5859375"},{"key":"proc.memory.percent","value_json":"0.567983309525807"},{"key":"cpu","value_json":"0.9971090307092721"},{"key":"proc.cpu.threads","value_json":"15"}]}}}
{"time":"2025-08-12T18:23:45.175541259-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:23:45.17556394-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"001pj0c38x3w\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:45.17557229-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"001pj0c38x3w","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.175580697-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"001pj0c38x3w","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.175586593-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"001pj0c38x3w","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.175625486-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:45.215704695-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:23:45.215719248-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"uymve7ncspzo\" connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:45.215728737-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"uymve7ncspzo","connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:45.215737051-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"uymve7ncspzo","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:45.331099613-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"001pj0c38x3w","connection_id":"127.0.0.1:53322"}}}
{"time":"2025-08-12T18:23:45.451485985-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"wandb-events.jsonl\":{\"offset\":2,\"content\":[\"{\\\"system.gpu.0.powerWatts\\\":112.163,\\\"system.gpu.0.correctedMemoryErrors\\\":0,\\\"system.gpu.2.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.2.powerWatts\\\":71.366,\\\"system.gpu.2.powerPercent\\\":10.195142857142857,\\\"system.gpu.2.memoryClock\\\":2619,\\\"_timestamp\\\":1.75504822509393e+09,\\\"system.gpu.0.memory\\\":0,\\\"system.gpu.3.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.1.smClock\\\":345,\\\"system.gpu.1.memoryClock\\\":2619,\\\"system.gpu.1.correctedMemoryErrors\\\":0,\\\"system.gpu.3.powerWatts\\\":71.709,\\\"system.gpu.3.smClock\\\":345,\\\"system.gpu.0.memoryAllocated\\\":1.8354810627889013,\\\"system.gpu.0.temp\\\":35,\\\"system.gpu.3.memory\\\":0,\\\"system.gpu.3.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.1.gpu\\\":0,\\\"system.gpu.1.memory\\\":0,\\\"system.gpu.1.temp\\\":27,\\\"system.gpu.1.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.3.memoryClock\\\":2619,\\\"_wandb\\\":true,\\\"system.gpu.1.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.3.gpu\\\":0,\\\"system.gpu.3.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.3.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.1.powerWatts\\\":71.543,\\\"system.gpu.2.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.2.gpu\\\":0,\\\"system.gpu.2.memory\\\":0,\\\"system.gpu.2.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.2.smClock\\\":345,\\\"system.gpu.2.correctedMemoryErrors\\\":0,\\\"system.gpu.3.temp\\\":33,\\\"system.gpu.0.smClock\\\":1980,\\\"system.gpu.1.powerPercent\\\":10.220428571428572,\\\"system.gpu.3.correctedMemoryErrors\\\":0,\\\"system.gpu.0.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.0.powerPercent\\\":16.023285714285716,\\\"system.gpu.0.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.1.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.2.temp\\\":28,\\\"_runtime\\\":30.408075896,\\\"system.gpu.0.memoryAllocatedBytes\\\":1569718272,\\\"system.gpu.1.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.2.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.3.powerPercent\\\":10.244142857142858,\\\"system.gpu.0.gpu\\\":0,\\\"system.gpu.0.memoryClock\\\":2619}\",\"{\\\"system.disk./.usagePercent\\\":1.9874612325289254,\\\"system.disk./.usageGB\\\":17.446334838867188,\\\"system.memory_percent\\\":2.465414004675286,\\\"system.proc.memory.rssMB\\\":11722.5859375,\\\"_wandb\\\":true,\\\"_runtime\\\":30.418662863,\\\"system.proc.memory.availableMB\\\":2.00196289453125e+06,\\\"system.network.sent\\\":1.3053195e+07,\\\"system.proc.memory.percent\\\":0.567983309525807,\\\"system.cpu\\\":0.9971090307092721,\\\"system.proc.cpu.threads\\\":15,\\\"_timestamp\\\":1.7550482251045167e+09,\\\"system.network.recv\\\":1.058999269e+09}\"]}}}"}
{"time":"2025-08-12T18:23:45.451539691-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/s8v7shll/file_stream"}
{"time":"2025-08-12T18:23:45.589735417-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:23:50.578100585-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":577945000},"line":"Traceback (most recent call last):\n"}}}
{"time":"2025-08-12T18:23:50.57812644-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.578140144-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":577945000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.578153572-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":577945000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.578160989-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":21,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":577945000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.578194565-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:23:50.578280804-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:50.581689649-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:23:50.618748576-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578031000}}}}
{"time":"2025-08-12T18:23:50.61876429-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.618776649-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578074000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}}}
{"time":"2025-08-12T18:23:50.618781062-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.618788779-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578091000}}}}
{"time":"2025-08-12T18:23:50.618792267-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:50.61879887-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578104000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}}}
{"time":"2025-08-12T18:23:50.618802472-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":2}
{"time":"2025-08-12T18:23:50.618808493-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578116000}}}}
{"time":"2025-08-12T18:23:50.618811655-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":3}
{"time":"2025-08-12T18:23:50.618819844-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578127000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}}}
{"time":"2025-08-12T18:23:50.618823432-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":4}
{"time":"2025-08-12T18:23:50.618829507-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578140000}}}}
{"time":"2025-08-12T18:23:50.61883264-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":5}
{"time":"2025-08-12T18:23:50.618838532-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578151000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}}}
{"time":"2025-08-12T18:23:50.618841939-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":6}
{"time":"2025-08-12T18:23:50.618849016-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578162000}}}}
{"time":"2025-08-12T18:23:50.618851931-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":7}
{"time":"2025-08-12T18:23:50.61885806-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578172000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}}}
{"time":"2025-08-12T18:23:50.618861163-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":8}
{"time":"2025-08-12T18:23:50.618868912-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578183000}}}}
{"time":"2025-08-12T18:23:50.618859095-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578031000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618886765-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578074000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618892778-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578091000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618873604-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":9}
{"time":"2025-08-12T18:23:50.61889594-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578104000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618899977-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578116000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618904272-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578193000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}}}
{"time":"2025-08-12T18:23:50.618902996-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578127000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618908199-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":5}
{"time":"2025-08-12T18:23:50.618909306-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578140000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618912374-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578151000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618907903-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578031000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618916239-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578162000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618928281-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578172000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618916009-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578207000}}}}
{"time":"2025-08-12T18:23:50.618932537-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578183000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618936094-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578193000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618959101-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.618920507-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578074000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618982696-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578219000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:23:50.61898374-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578091000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618989149-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.618988813-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578104000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618992736-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578116000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618995558-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578127000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.618999614-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578231000}}}}
{"time":"2025-08-12T18:23:50.619001194-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578140000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619004083-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:50.619004244-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578151000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619007612-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578162000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619009917-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578172000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619012551-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578183000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619014631-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578241000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}}}
{"time":"2025-08-12T18:23:50.619014937-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578193000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619019288-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":2}
{"time":"2025-08-12T18:23:50.619026263-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578253000}}}}
{"time":"2025-08-12T18:23:50.619029683-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":3}
{"time":"2025-08-12T18:23:50.619036392-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578263000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n    self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n"}}}
{"time":"2025-08-12T18:23:50.619041528-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":4}
{"time":"2025-08-12T18:23:50.619040796-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578207000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619045964-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578219000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619047755-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578274000}}}}
{"time":"2025-08-12T18:23:50.619049068-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578231000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619051894-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":3}
{"time":"2025-08-12T18:23:50.619052112-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578241000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619055458-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578253000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619057657-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578263000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n    self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619059201-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578284000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n    self.restore_model()\n"}}}
{"time":"2025-08-12T18:23:50.619060862-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578274000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619064173-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619065298-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578207000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619073841-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578284000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n    self.restore_model()\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619070858-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578295000}}}}
{"time":"2025-08-12T18:23:50.619084419-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619075903-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578219000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619091673-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n    self.trainer.strategy.load_model_state_dict(\n"}}}
{"time":"2025-08-12T18:23:50.619092617-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578231000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.6190971-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619105279-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578317000}}}}
{"time":"2025-08-12T18:23:50.619104417-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578295000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619109498-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.61910934-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n    self.trainer.strategy.load_model_state_dict(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619113352-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578317000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619096136-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578241000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619139509-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578253000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619156057-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578263000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n    self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619161342-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578274000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619164211-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578284000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n    self.restore_model()\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619167549-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578295000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619170176-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n    self.trainer.strategy.load_model_state_dict(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619173002-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578317000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.61911815-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578327000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n    self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n"}}}
{"time":"2025-08-12T18:23:50.619192894-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619209688-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578338000}}}}
{"time":"2025-08-12T18:23:50.619214755-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619222864-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578349000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n    return super().load_state_dict(state_dict, strict=False)\n"}}}
{"time":"2025-08-12T18:23:50.619228908-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:50.619236233-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578360000}}}}
{"time":"2025-08-12T18:23:50.619239991-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":2}
{"time":"2025-08-12T18:23:50.619246518-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578370000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n    raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n"}}}
{"time":"2025-08-12T18:23:50.619251107-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":3}
{"time":"2025-08-12T18:23:50.619257746-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578381000}}}}
{"time":"2025-08-12T18:23:50.619261204-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":4}
{"time":"2025-08-12T18:23:50.61926101-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578327000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n    self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619270729-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578338000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619273456-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578349000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n    return super().load_state_dict(state_dict, strict=False)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.61927643-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578360000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619279806-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578370000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n    raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619283055-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578381000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619289435-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578327000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n    self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619283626-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578400000},"line":"RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n\tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}}}
{"time":"2025-08-12T18:23:50.619297697-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619304498-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578418000}}}}
{"time":"2025-08-12T18:23:50.619307637-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":0}
{"time":"2025-08-12T18:23:50.619292999-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578338000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619331649-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":581432000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n[rank0]:     self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n[rank0]:     self.restore_model()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n[rank0]:     self.trainer.strategy.load_model_state_dict(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n[rank0]:     self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n[rank0]:     return super().load_state_dict(state_dict, strict=False)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n[rank0]:     raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n[rank0]: RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}}}
{"time":"2025-08-12T18:23:50.619341936-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578349000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n    return super().load_state_dict(state_dict, strict=False)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619348784-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578360000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619351787-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578370000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n    raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619357435-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578381000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.61934644-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:53322\")","buffer":1}
{"time":"2025-08-12T18:23:50.619349135-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578400000},"line":"RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n\tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619373216-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578418000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619376799-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":581432000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n[rank0]:     self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n[rank0]:     self.restore_model()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n[rank0]:     self.trainer.strategy.load_model_state_dict(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n[rank0]:     self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n[rank0]:     return super().load_state_dict(state_dict, strict=False)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n[rank0]:     raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n[rank0]: RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619393302-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578400000},"line":"RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n\tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619404861-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578418000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.619407497-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":581432000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n[rank0]:     self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n[rank0]:     self.restore_model()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n[rank0]:     self.trainer.strategy.load_model_state_dict(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n[rank0]:     self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n[rank0]:     return super().load_state_dict(state_dict, strict=False)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n[rank0]:     raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n[rank0]: RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739226622-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":22,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578031000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739244666-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":23,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578074000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739262301-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":24,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578091000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739265388-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":25,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578104000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739280494-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":26,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578116000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739283156-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":27,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578127000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739317214-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":28,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578140000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739320013-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":29,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578151000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739360884-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":30,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578162000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739363678-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":31,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578172000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.73941207-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":32,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578183000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739414843-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":33,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578193000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739462581-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":34,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578207000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739465372-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":35,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578219000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739499726-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":36,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578231000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.73950246-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":37,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578241000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739530216-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":38,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578253000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739532722-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":39,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578263000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n    self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739588808-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":40,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578274000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739591541-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":41,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578284000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n    self.restore_model()\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.73964308-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":42,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578295000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.73964588-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":43,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n    self.trainer.strategy.load_model_state_dict(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739681397-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":44,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578317000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739684035-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":45,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578327000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n    self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739714253-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":46,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578338000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739716696-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":47,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578349000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n    return super().load_state_dict(state_dict, strict=False)\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739752754-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":48,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578360000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739755655-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":49,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578370000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n    raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739792154-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":50,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578381000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.739794729-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":51,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578400000},"line":"RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n\tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n\tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n\tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n\tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.741930347-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":52,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":578418000}}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.74195442-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":53,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755048230,"nanos":581432000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 955, in _run\n[rank0]:     self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 398, in _restore_modules_and_callbacks\n[rank0]:     self.restore_model()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 275, in restore_model\n[rank0]:     self.trainer.strategy.load_model_state_dict(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 371, in load_model_state_dict\n[rank0]:     self.lightning_module.load_state_dict(checkpoint[\"state_dict\"], strict=strict)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 172, in load_state_dict\n[rank0]:     return super().load_state_dict(state_dict, strict=False)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 2215, in load_state_dict\n[rank0]:     raise RuntimeError('Error(s) in loading state_dict for {}:\\n\\t{}'.format(\n[rank0]: RuntimeError: Error(s) in loading state_dict for PromptMrModule:\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\n[rank0]: \tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\n"}},"control":{"connection_id":"127.0.0.1:53322"},"_info":{"stream_id":"s8v7shll"}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:50.744096731-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:23:50.754397461-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:23:51.338645307-07:00","level":"DEBUG","msg":"handling record","record":{"Exit":{"exit_code":1}}}
{"time":"2025-08-12T18:23:51.338687191-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Exit); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:23:51.338692033-07:00","level":"INFO","msg":"stream: closing","id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338695041-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.3387057-07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-12T18:23:51.338710647-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"36"}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338733116-07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-12T18:23:51.338729783-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":54,"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"36"}]}}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338747047-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:23:51.338739911-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1,"runtime":36}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.33875158-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Record":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"36"}]}}}
{"time":"2025-08-12T18:23:51.338759903-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":55,"RecordType":{"Exit":{"exit_code":1,"runtime":36}},"control":{"always_send":true}}},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338782176-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(1))","buffer":0}
{"time":"2025-08-12T18:23:51.338785477-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338800494-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338802891-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.33881159-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(2))","buffer":0}
{"time":"2025-08-12T18:23:51.33881427-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338816455-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.338818682-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.344007663-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:23:51.344069956-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:51.389404846-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["output.log","wandb-summary.json"]}
{"time":"2025-08-12T18:23:51.389490204-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:51.44989162-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(3))","buffer":0}
{"time":"2025-08-12T18:23:51.449905422-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.449913201-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.449917075-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.449922596-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(4))","buffer":0}
{"time":"2025-08-12T18:23:51.449925342-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.449927842-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.449930517-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/output.log, Name: output.log, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=8a8db6e4c44764c438492ae2d9932856918fb3c8ed09c95177bcb75c7ad9c25cb322cd9ec1425f2c854fc46a32db4e5bbb5e0d895ea99802beebffcb0cb24ccd433a612dc8e2c9e1bc0f99892e3fd2c694a88e3a3a1997d068cf101efc633732c88e76d2060b7992c2c88ab5c3c58ac9a2073577017f4584b58e4898aeb6b4a042f61a740553c01b0d247997fe03a0eec4851a714087cf779b86ea526fe7b5c7ea4144cffb7f8cd6b5a082ba8861e6fe7f9f8f858606f24376d019d6e319b7280f3a85a136505456f75b97e9c5d66f4cb6b69fd2b6daebc413e890829405be59e2a5b50d826f04f96d3dee0476ba527fc28f3ae921e8ee37ba420c30514d03c8&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/wandb-summary.json, Name: wandb-summary.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=53f734e12641a9277103b8291203cfdf00a96a8273a2cf81319ef8d4cf585f2d3d16e1c311bba7c299f229f2f9193ae23b01ccd8e51cfc8a8474e4361b94b60bf2bfd02b1f3bb32132ba7b4150c4f4404660b2a4a3bf62e5f25ff2734b2c64737a0359987a13ffd9b4f307c45fe8d5e37e28689b59c677755a2879a11ec090c210e5a562ec2b8ff6902a2fd47d265e65081a516ceda59009103ca78d0379bacc70a7e1ef79278e96a6977bc7ffd0cfced73f312cc8dae6561ce7e658a9d9e900b26c796d3974d6fa963509794517937bd3907ee294e343d1a3e5c7457b93243f7ca9fb3d485c92da2eca9baa2bdc8f33f8513b957c5aebd2fd69ad000122cfed&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/wandb-summary.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=53f734e12641a9277103b8291203cfdf00a96a8273a2cf81319ef8d4cf585f2d3d16e1c311bba7c299f229f2f9193ae23b01ccd8e51cfc8a8474e4361b94b60bf2bfd02b1f3bb32132ba7b4150c4f4404660b2a4a3bf62e5f25ff2734b2c64737a0359987a13ffd9b4f307c45fe8d5e37e28689b59c677755a2879a11ec090c210e5a562ec2b8ff6902a2fd47d265e65081a516ceda59009103ca78d0379bacc70a7e1ef79278e96a6977bc7ffd0cfced73f312cc8dae6561ce7e658a9d9e900b26c796d3974d6fa963509794517937bd3907ee294e343d1a3e5c7457b93243f7ca9fb3d485c92da2eca9baa2bdc8f33f8513b957c5aebd2fd69ad000122cfed&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/output.log","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=8a8db6e4c44764c438492ae2d9932856918fb3c8ed09c95177bcb75c7ad9c25cb322cd9ec1425f2c854fc46a32db4e5bbb5e0d895ea99802beebffcb0cb24ccd433a612dc8e2c9e1bc0f99892e3fd2c694a88e3a3a1997d068cf101efc633732c88e76d2060b7992c2c88ab5c3c58ac9a2073577017f4584b58e4898aeb6b4a042f61a740553c01b0d247997fe03a0eec4851a714087cf779b86ea526fe7b5c7ea4144cffb7f8cd6b5a082ba8861e6fe7f9f8f858606f24376d019d6e319b7280f3a85a136505456f75b97e9c5d66f4cb6b69fd2b6daebc413e890829405be59e2a5b50d826f04f96d3dee0476ba527fc28f3ae921e8ee37ba420c30514d03c8&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=8a8db6e4c44764c438492ae2d9932856918fb3c8ed09c95177bcb75c7ad9c25cb322cd9ec1425f2c854fc46a32db4e5bbb5e0d895ea99802beebffcb0cb24ccd433a612dc8e2c9e1bc0f99892e3fd2c694a88e3a3a1997d068cf101efc633732c88e76d2060b7992c2c88ab5c3c58ac9a2073577017f4584b58e4898aeb6b4a042f61a740553c01b0d247997fe03a0eec4851a714087cf779b86ea526fe7b5c7ea4144cffb7f8cd6b5a082ba8861e6fe7f9f8f858606f24376d019d6e319b7280f3a85a136505456f75b97e9c5d66f4cb6b69fd2b6daebc413e890829405be59e2a5b50d826f04f96d3dee0476ba527fc28f3ae921e8ee37ba420c30514d03c8&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=53f734e12641a9277103b8291203cfdf00a96a8273a2cf81319ef8d4cf585f2d3d16e1c311bba7c299f229f2f9193ae23b01ccd8e51cfc8a8474e4361b94b60bf2bfd02b1f3bb32132ba7b4150c4f4404660b2a4a3bf62e5f25ff2734b2c64737a0359987a13ffd9b4f307c45fe8d5e37e28689b59c677755a2879a11ec090c210e5a562ec2b8ff6902a2fd47d265e65081a516ceda59009103ca78d0379bacc70a7e1ef79278e96a6977bc7ffd0cfced73f312cc8dae6561ce7e658a9d9e900b26c796d3974d6fa963509794517937bd3907ee294e343d1a3e5c7457b93243f7ca9fb3d485c92da2eca9baa2bdc8f33f8513b957c5aebd2fd69ad000122cfed&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["config.yaml"]}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/config.yaml, Name: config.yaml, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=68e115cac8480cb9577c93704783c25c7f9d8bfddaf581b293a161cda69e8af66db2b63b25fa1905b90c4b454402936f07e7dc2fdd25a75dcc418298e0b803be89b4537eac8317cf85e887ecfea723cc456efb8c0460ee670875c5c4588ad846201e357f57c59383dc6b6b4511c7fc5d0c6332ffd162f18ca498dc90abd026fd1f68d391801cda31827066bd5bb4b5a85b4d1e7a0566f79d6cff1a89a1d91a132fe98b2a8d5c3314df5199d665d0b9dd6ad4852e6c14e22c852f72eef55e8f55f7941d2814c8e8bfa890d3f2c432e4e3a884eb9268a5a497cfd19eb8b1368a255c113e81e947a5acdab20367d41eb5bc69b18bc9ac76a880752cb20f7f2a79b4&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_182314-s8v7shll/files/config.yaml","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=68e115cac8480cb9577c93704783c25c7f9d8bfddaf581b293a161cda69e8af66db2b63b25fa1905b90c4b454402936f07e7dc2fdd25a75dcc418298e0b803be89b4537eac8317cf85e887ecfea723cc456efb8c0460ee670875c5c4588ad846201e357f57c59383dc6b6b4511c7fc5d0c6332ffd162f18ca498dc90abd026fd1f68d391801cda31827066bd5bb4b5a85b4d1e7a0566f79d6cff1a89a1d91a132fe98b2a8d5c3314df5199d665d0b9dd6ad4852e6c14e22c852f72eef55e8f55f7941d2814c8e8bfa890d3f2c432e4e3a884eb9268a5a497cfd19eb8b1368a255c113e81e947a5acdab20367d41eb5bc69b18bc9ac76a880752cb20f7f2a79b4&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/s8v7shll/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T012351Z&X-Goog-Expires=86399&X-Goog-Signature=68e115cac8480cb9577c93704783c25c7f9d8bfddaf581b293a161cda69e8af66db2b63b25fa1905b90c4b454402936f07e7dc2fdd25a75dcc418298e0b803be89b4537eac8317cf85e887ecfea723cc456efb8c0460ee670875c5c4588ad846201e357f57c59383dc6b6b4511c7fc5d0c6332ffd162f18ca498dc90abd026fd1f68d391801cda31827066bd5bb4b5a85b4d1e7a0566f79d6cff1a89a1d91a132fe98b2a8d5c3314df5199d665d0b9dd6ad4852e6c14e22c852f72eef55e8f55f7941d2814c8e8bfa890d3f2c432e4e3a884eb9268a5a497cfd19eb8b1368a255c113e81e947a5acdab20367d41eb5bc69b18bc9ac76a880752cb20f7f2a79b4&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-summary.json"}}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"output.log"}}
{"time":"2025-08-12T18:23:51.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"config.yaml"}}
{"time":"2025-08-12T18:23:51.808928297-07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-12T18:23:51.808936068-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(5))","buffer":0}
{"time":"2025-08-12T18:23:51.808942144-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.808946547-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.808949591-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.808953473-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"ExitCode":1}}
{"time":"2025-08-12T18:23:51.809070515-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":3,\"content\":[\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.578176\\\",\\\"content\\\":\\\"Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739248\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739258\\\",\\\"content\\\":\\\"    run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739267\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739276\\\",\\\"content\\\":\\\"    cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739285\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739310\\\",\\\"content\\\":\\\"    self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739327\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739357\\\",\\\"content\\\":\\\"    fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739366\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739405\\\",\\\"content\\\":\\\"    call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739417\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739445\\\",\\\"content\\\":\\\"    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739467\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739494\\\",\\\"content\\\":\\\"    return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739505\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739524\\\",\\\"content\\\":\\\"    self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739536\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 955, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739569\\\",\\\"content\\\":\\\"    self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739593\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 398, in _restore_modules_and_callbacks\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739637\\\",\\\"content\\\":\\\"    self.restore_model()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739648\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 275, in restore_model\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739671\\\",\\\"content\\\":\\\"    self.trainer.strategy.load_model_state_dict(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739686\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 371, in load_model_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739705\\\",\\\"content\\\":\\\"    self.lightning_module.load_state_dict(checkpoint[\\\\\\\"state_dict\\\\\\\"], strict=strict)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739719\\\",\\\"content\\\":\\\"  File \\\\\\\"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\\\\\\\", line 172, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739741\\\",\\\"content\\\":\\\"    return super().load_state_dict(state_dict, strict=False)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739760\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 2215, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739777\\\",\\\"content\\\":\\\"    raise RuntimeError('Error(s) in loading state_dict for {}:\\\\\\\\n\\\\\\\\t{}'.format(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739803\\\",\\\"content\\\":\\\"RuntimeError: Error(s) in loading state_dict for PromptMrModule:\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739812\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739845\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739872\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739896\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739922\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739949\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.739976\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.740000\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.740025\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.740051\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.740339\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.740495\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.740535\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741348\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741535\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741577\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741606\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741646\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741677\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741722\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741753\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741787\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741818\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741846\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741870\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741896\\\",\\\"content\\\":\\\"\\\\tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741983\\\",\\\"content\\\":\\\"[rank0]: Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741989\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741994\\\",\\\"content\\\":\\\"[rank0]:     run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.741996\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742002\\\",\\\"content\\\":\\\"[rank0]:     cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742005\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742026\\\",\\\"content\\\":\\\"[rank0]:     self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742031\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742048\\\",\\\"content\\\":\\\"[rank0]:     fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742050\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742067\\\",\\\"content\\\":\\\"[rank0]:     call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742072\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742090\\\",\\\"content\\\":\\\"[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742100\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742120\\\",\\\"content\\\":\\\"[rank0]:     return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742124\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742144\\\",\\\"content\\\":\\\"[rank0]:     self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742149\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 955, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742166\\\",\\\"content\\\":\\\"[rank0]:     self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742174\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 398, in _restore_modules_and_callbacks\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742200\\\",\\\"content\\\":\\\"[rank0]:     self.restore_model()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742204\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 275, in restore_model\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742224\\\",\\\"content\\\":\\\"[rank0]:     self.trainer.strategy.load_model_state_dict(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742229\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 371, in load_model_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742250\\\",\\\"content\\\":\\\"[rank0]:     self.lightning_module.load_state_dict(checkpoint[\\\\\\\"state_dict\\\\\\\"], strict=strict)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742259\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\\\\\\\", line 172, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742274\\\",\\\"content\\\":\\\"[rank0]:     return super().load_state_dict(state_dict, strict=False)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742281\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 2215, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742296\\\",\\\"content\\\":\\\"[rank0]:     raise RuntimeError('Error(s) in loading state_dict for {}:\\\\\\\\n\\\\\\\\t{}'.format(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742304\\\",\\\"content\\\":\\\"[rank0]: RuntimeError: Error(s) in loading state_dict for PromptMrModule:\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742311\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742343\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742368\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742393\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742418\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742444\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742471\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742499\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742523\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742549\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742577\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742602\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742641\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742672\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742700\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742726\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742753\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742779\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742813\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742844\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742871\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742896\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742922\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742947\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742971\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:23:50.742999\\\",\\\"content\\\":\\\"[rank0]: \\\\tsize mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).\\\"}\"]},\"wandb-summary.json\":{\"offset\":0,\"content\":[\"{\\\"_wandb\\\":{\\\"runtime\\\":36}}\"]}},\"uploaded\":[\"config.yaml\",\"wandb-summary.json\",\"output.log\"],\"complete\":true,\"exitcode\":1}"}
{"time":"2025-08-12T18:23:51.809168602-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/s8v7shll/file_stream"}
{"time":"2025-08-12T18:23:51.934874992-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":1,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:23:51.93491716-07:00","level":"DEBUG","msg":"filestream: closed"}
{"time":"2025-08-12T18:23:51.934924033-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(6))","buffer":0}
{"time":"2025-08-12T18:23:51.934929572-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.934933752-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.934936818-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.934943783-07:00","level":"INFO","msg":"handler: closed","stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.934946569-07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.934978094-07:00","level":"INFO","msg":"sender: closed","stream_id":"s8v7shll"}
{"time":"2025-08-12T18:23:51.937753223-07:00","level":"INFO","msg":"stream: closed","id":"s8v7shll"}
