Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/s8v7shll/config.yaml
Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
Traceback (most recent call last):
  File "main.py", line 244, in <module>
    run_cli()
  File "main.py", line 236, in run_cli
    cli = CustomLightningCLI(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 394, in __init__
    self._run_subcommand(self.subcommand)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 701, in _run_subcommand
    fn(**fn_kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 543, in fit
    call._call_and_handle_interrupt(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 43, in _call_and_handle_interrupt
    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
    return function(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 579, in _fit_impl
    self._run(model, ckpt_path=ckpt_path)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 955, in _run
    self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 398, in _restore_modules_and_callbacks
    self.restore_model()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 275, in restore_model
    self.trainer.strategy.load_model_state_dict(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 371, in load_model_state_dict
    self.lightning_module.load_state_dict(checkpoint["state_dict"], strict=strict)
  File "/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py", line 172, in load_state_dict
    return super().load_state_dict(state_dict, strict=False)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 2215, in load_state_dict
    raise RuntimeError('Error(s) in loading state_dict for {}:\n\t{}'.format(
RuntimeError: Error(s) in loading state_dict for PromptMrModule:
	size mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).
	size mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).
	size mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
	size mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
	size mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: Traceback (most recent call last):
[rank0]:   File "main.py", line 244, in <module>
[rank0]:     run_cli()
[rank0]:   File "main.py", line 236, in run_cli
[rank0]:     cli = CustomLightningCLI(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 394, in __init__
[rank0]:     self._run_subcommand(self.subcommand)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 701, in _run_subcommand
[rank0]:     fn(**fn_kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 543, in fit
[rank0]:     call._call_and_handle_interrupt(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 43, in _call_and_handle_interrupt
[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
[rank0]:     return function(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 579, in _fit_impl
[rank0]:     self._run(model, ckpt_path=ckpt_path)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 955, in _run
[rank0]:     self._checkpoint_connector._restore_modules_and_callbacks(ckpt_path)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 398, in _restore_modules_and_callbacks
[rank0]:     self.restore_model()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 275, in restore_model
[rank0]:     self.trainer.strategy.load_model_state_dict(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 371, in load_model_state_dict
[rank0]:     self.lightning_module.load_state_dict(checkpoint["state_dict"], strict=strict)
[rank0]:   File "/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py", line 172, in load_state_dict
[rank0]:     return super().load_state_dict(state_dict, strict=False)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 2215, in load_state_dict
[rank0]:     raise RuntimeError('Error(s) in loading state_dict for {}:\n\t{}'.format(
[rank0]: RuntimeError: Error(s) in loading state_dict for PromptMrModule:
[rank0]: 	size mismatch for promptmr.sens_net.norm_unet.unet.feat_extract.weight: copying a param with shape torch.Size([24, 14, 3, 3]) from checkpoint, the shape in current model is torch.Size([24, 10, 3, 3]).
[rank0]: 	size mismatch for promptmr.sens_net.norm_unet.unet.conv_last.weight: copying a param with shape torch.Size([14, 24, 5, 5]) from checkpoint, the shape in current model is torch.Size([10, 24, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.0.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.0.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.1.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.1.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.2.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.2.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.3.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.3.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.4.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.4.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.5.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.5.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.6.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.6.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.7.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.7.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.8.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.8.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.9.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.9.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.10.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.10.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
[rank0]: 	size mismatch for promptmr.cascades.11.model.unet.feat_extract.weight: copying a param with shape torch.Size([48, 70, 3, 3]) from checkpoint, the shape in current model is torch.Size([48, 50, 3, 3]).
[rank0]: 	size mismatch for promptmr.cascades.11.model.unet.conv_last.weight: copying a param with shape torch.Size([70, 48, 5, 5]) from checkpoint, the shape in current model is torch.Size([50, 48, 5, 5]).
