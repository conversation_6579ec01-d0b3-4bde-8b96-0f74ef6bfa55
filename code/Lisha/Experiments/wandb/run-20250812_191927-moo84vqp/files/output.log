Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/moo84vqp/config.yaml
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                 | Params | Mode
------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum | 0      | train
1 | SSIM             | DistributedMetricSum | 0      | train
2 | PSNR             | DistributedMetricSum | 0      | train
3 | ValLoss          | DistributedMetricSum | 0      | train
4 | TotExamples      | DistributedMetricSum | 0      | train
5 | TotSliceExamples | DistributedMetricSum | 0      | train
6 | promptmr         | PromptMR             | 125 M  | train
7 | loss             | SSIMLoss             | 0      | train
------------------------------------------------------------------
111 <USER>     <GROUP> params
13.7 M    Non-trainable params
125 M     Total params
502.227   Total estimated model params size (MB)
SLURM auto-requeueing enabled. Setting signal handlers.
Epoch 2:  17%|█▋        | 9655/55879 [1:17:37<6:11:36,  2.07it/s, v_num=4vqp, train_loss=0.00582, validation_loss=0.0248]  
                                                                            
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory
  warn(f"Failed to load image Python extension: {e}")
Epoch 0, global step 55879: 'validation_loss' reached 0.02550 (best 0.02550), saving model to '/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/moo84vqp/checkpoints/best-epochepoch=00-valvalidation_loss=0.0255.ckpt' as top 1
Epoch 1, global step 111758: 'validation_loss' reached 0.02484 (best 0.02484), saving model to '/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/moo84vqp/checkpoints/best-epochepoch=01-valvalidation_loss=0.0248.ckpt' as top 1
