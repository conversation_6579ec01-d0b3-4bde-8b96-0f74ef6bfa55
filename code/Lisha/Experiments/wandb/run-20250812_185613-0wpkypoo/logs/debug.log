2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_setup.py:_flush():67] Configure stats pid to 1719219
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_setup.py:_flush():67] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_setup.py:_flush():67] Loading settings from /common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/wandb/settings
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/logs/debug.log
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/logs/debug-internal.log
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_init.py:init():781] calling init triggers
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_init.py:init():809] starting backend
2025-08-12 18:56:13,269 INFO    MainThread:1719219 [wandb_init.py:init():813] sending inform_init request
2025-08-12 18:56:13,288 INFO    MainThread:1719219 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-08-12 18:56:13,288 INFO    MainThread:1719219 [wandb_init.py:init():823] backend started and connected
2025-08-12 18:56:13,289 DEBUG   MainThread:1719219 [config_util.py:dict_from_config_file():70] no default config file found in config-defaults.yaml
2025-08-12 18:56:13,291 INFO    MainThread:1719219 [wandb_init.py:init():915] updated telemetry
2025-08-12 18:56:13,291 INFO    MainThread:1719219 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-08-12 18:56:13,805 INFO    MainThread:1719219 [wandb_init.py:init():1014] starting run threads in backend
2025-08-12 18:56:13,812 DEBUG   MainThread:1719219 [wandb_run.py:_on_start():2486] Saving list of pip packages installed into the current environment
2025-08-12 18:56:13,902 INFO    MainThread:1719219 [wandb_run.py:_console_start():2454] atexit reg
2025-08-12 18:56:13,902 INFO    MainThread:1719219 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-12 18:56:13,902 INFO    MainThread:1719219 [wandb_run.py:_redirect():2371] Wrapping output streams.
2025-08-12 18:56:13,902 INFO    MainThread:1719219 [wandb_run.py:_redirect():2394] Redirects installed.
2025-08-12 18:56:13,905 INFO    MainThread:1719219 [wandb_init.py:init():1056] run started, returning control to user process
2025-08-12 18:56:14,797 INFO    MainThread:1719219 [wandb_run.py:_config_callback():1327] config_cb None None {'class_path': 'pl_modules.PromptMrModule', 'init_args': {'num_cascades': 16, 'num_adj_slices': 7, 'n_feat0': 48, 'feature_dim': [72, 96, 120], 'prompt_dim': [24, 48, 72], 'sens_n_feat0': 24, 'sens_feature_dim': [36, 48, 60], 'sens_prompt_dim': [12, 24, 36], 'len_prompt': [5, 5, 5], 'prompt_size': [64, 32, 16], 'n_enc_cab': [2, 3, 3], 'n_dec_cab': [2, 2, 3], 'n_skip_cab': [1, 1, 1], 'n_bottleneck_cab': 3, 'no_use_ca': False, 'learnable_prompt': False, 'adaptive_input': True, 'n_buffer': 4, 'n_history': 15, 'use_sens_adj': True, 'model_version': 'promptmr_v2', 'lr': 0.0002, 'lr_step_size': 11, 'lr_gamma': 0.1, 'weight_decay': 0.01, 'use_checkpoint': False, 'compute_sens_per_coil': False, 'pretrain': True, 'pretrain_weights_path': '/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt', 'num_log_images': 16}, '_instantiator': 'lightning.pytorch.cli.instantiate_module'}
2025-08-12 18:56:43,910 INFO    MsgRouterThr:1719219 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 2 handles.
