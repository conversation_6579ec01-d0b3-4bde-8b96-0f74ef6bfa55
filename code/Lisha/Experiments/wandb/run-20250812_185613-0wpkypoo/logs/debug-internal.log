{"time":"2025-08-12T18:56:13.294830415-07:00","level":"INFO","msg":"stream: starting","core version":"0.19.9","symlink path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/logs/debug-core.log"}
{"time":"2025-08-12T18:56:13.29490645-07:00","level":"DEBUG","msg":"monitor: sampling interval: 15s"}
{"time":"2025-08-12T18:56:13.509191545-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:13.585055496-07:00","level":"INFO","msg":"created new stream","id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.585078484-07:00","level":"INFO","msg":"stream: started","id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.58509329-07:00","level":"INFO","msg":"writer: Do: started","stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.585111766-07:00","level":"INFO","msg":"handler: started","stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.585116792-07:00","level":"INFO","msg":"sender: started","stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.586874131-07:00","level":"DEBUG","msg":"handling record","record":{"Header":{}}}
{"time":"2025-08-12T18:56:13.586891724-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Header); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:13.586959875-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Header":{}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.587001084-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.587009952-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":1,"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.587246826-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"0wpkypoo","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:56:13.587261004-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(mailbox_slot:\"ni360ajkmyri\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:13.587269911-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"0wpkypoo","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"ni360ajkmyri","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.587294083-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"0wpkypoo","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"ni360ajkmyri","always_send":true,"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.587321109-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":2,"RecordType":{"Run":{"run_id":"0wpkypoo","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"ni360ajkmyri","always_send":true,"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.587411282-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:13.804756624-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"RunResult":{"run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","storage_id":"UnVuOnYxOjB3cGt5cG9vOmNtcjIwMjVfdGFzazM6bGlzaGEtemVuZy1jZWRhcnMtc2luYWk=","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}},"control":{"mailbox_slot":"ni360ajkmyri","always_send":true,"connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:13.806370467-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}}}
{"time":"2025-08-12T18:56:13.806394586-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_RunStart); Control(local:true mailbox_slot:\"ww1xeqvyvsld\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:13.806405077-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"ww1xeqvyvsld","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.806494422-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"ww1xeqvyvsld","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.806536903-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"ww1xeqvyvsld","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.806549806-07:00","level":"DEBUG","msg":"filestream: start","path":""}
{"time":"2025-08-12T18:56:13.806570553-07:00","level":"DEBUG","msg":"filestream: open","path":"files/lisha-zeng-cedars-sinai/cmr2025_task3/0wpkypoo/file_stream"}
{"time":"2025-08-12T18:56:13.81175662-07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-12T18:56:13.811771775-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":null}},"control":{"local":true,"mailbox_slot":"ww1xeqvyvsld","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:13.811822394-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.811866022-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":3,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.840822346-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_Metadata); Control(<nil>)","buffer":0}
{"time":"2025-08-12T18:56:13.84084014-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"Metadata":{"cpu_count":96,"cpu_count_logical":96,"gpu_type":"NVIDIA H100 80GB HBM3","gpu_count":4,"disk":{"/":{"total":942552190976,"used":18732711936}},"memory":{"total":2164152020992},"cpu":{"count":96,"count_logical":96},"gpu_nvidia":[{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"}],"slurm":{"cluster_name":"slurm-compbio","conf":"/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf","cpu_bind":"quiet,mask_cpu:0x00000000000F000000000017","cpu_bind_list":"0x00000000000F000000000017","cpu_bind_type":"mask_cpu:","cpu_bind_verbose":"quiet","cpus_on_node":"8","cpus_per_task":"8","distribution":"cyclic","gpus_on_node":"4","gtids":"0","job_account":"user","job_cpus_per_node":"8","job_end_time":"**********","job_gid":"23023","job_gpus":"0,1,2,3","job_id":"2155125","job_name":"cmr_baselineXL_task3","job_nodelist":"esplhpc-cp088","job_num_nodes":"1","job_partition":"gpu","job_qos":"normal","job_start_time":"**********","job_uid":"1235884","job_user":"zengl2","jobid":"2155125","launch_node_ipaddr":"************","localid":"0","mem_per_node":"131072","nnodes":"1","nodeid":"0","nodelist":"esplhpc-cp088","nprocs":"1","ntasks":"1","prio_process":"0","procid":"0","srun_comm_host":"************","srun_comm_port":"37979","step_gpus":"0,1,2,3","step_id":"0","step_launcher_port":"37979","step_nodelist":"esplhpc-cp088","step_num_nodes":"1","step_num_tasks":"1","step_tasks_per_node":"1","stepid":"0","submit_dir":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3","submit_host":"esplhpccompbio-lv02","task_pid":"1719219","tasks_per_node":"1","topology_addr":"esplhpc-cp088","topology_addr_pattern":"node","tres_per_task":"cpu:8","umask":"0022"},"cuda_version":"12.4"}}}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.844456838-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.844466907-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":4,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.862919198-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["wandb-metadata.json"]}
{"time":"2025-08-12T18:56:13.863011004-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:13.902020979-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}}}
{"time":"2025-08-12T18:56:13.902048201-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_PythonPackages); Control(local:true connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:13.902057469-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}},"control":{"local":true,"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.903504906-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.903529761-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":5,"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/wandb-metadata.json, Name: wandb-metadata.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015613Z&X-Goog-Expires=86399&X-Goog-Signature=44895ce42d4999541cfa64a37b06a340b26c7561bff48534cc571713a8281ddad6b07fa8b5e954e5df4ea6382484aacdf58927e4082d61349ed44c3b9482d5a08adfe683edbf66f446ab8464cae897791e4ed022762e25692578e08ef3fef81971763dfe991b102d5402fc0537aa851a2df9995d3ae7d48c1102d5e36c5ead9829ce75d7b5d344a544af2bde00b522156aca8cf71cbbf18ccf820abfd0291be7a7357ddbc613c65f8261fa7827523a8a82d98720fb08c6daec730a974b95212fec9cb66cb5451a384ecbc8d1e70a9ed5b3691d63d89e309347e7f4903ce57082fa87bcc9145ddf3db95d755f2ddb3cb0cc69bd7b6d6a3883b4bc29196be7bfbf&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:56:13.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/wandb-metadata.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015613Z&X-Goog-Expires=86399&X-Goog-Signature=44895ce42d4999541cfa64a37b06a340b26c7561bff48534cc571713a8281ddad6b07fa8b5e954e5df4ea6382484aacdf58927e4082d61349ed44c3b9482d5a08adfe683edbf66f446ab8464cae897791e4ed022762e25692578e08ef3fef81971763dfe991b102d5402fc0537aa851a2df9995d3ae7d48c1102d5e36c5ead9829ce75d7b5d344a544af2bde00b522156aca8cf71cbbf18ccf820abfd0291be7a7357ddbc613c65f8261fa7827523a8a82d98720fb08c6daec730a974b95212fec9cb66cb5451a384ecbc8d1e70a9ed5b3691d63d89e309347e7f4903ce57082fa87bcc9145ddf3db95d755f2ddb3cb0cc69bd7b6d6a3883b4bc29196be7bfbf&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:13.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015613Z&X-Goog-Expires=86399&X-Goog-Signature=44895ce42d4999541cfa64a37b06a340b26c7561bff48534cc571713a8281ddad6b07fa8b5e954e5df4ea6382484aacdf58927e4082d61349ed44c3b9482d5a08adfe683edbf66f446ab8464cae897791e4ed022762e25692578e08ef3fef81971763dfe991b102d5402fc0537aa851a2df9995d3ae7d48c1102d5e36c5ead9829ce75d7b5d344a544af2bde00b522156aca8cf71cbbf18ccf820abfd0291be7a7357ddbc613c65f8261fa7827523a8a82d98720fb08c6daec730a974b95212fec9cb66cb5451a384ecbc8d1e70a9ed5b3691d63d89e309347e7f4903ce57082fa87bcc9145ddf3db95d755f2ddb3cb0cc69bd7b6d6a3883b4bc29196be7bfbf&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:13.*********-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:56:13.*********-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"t3fz86c38x24\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:13.943789949-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:56:13.94380096-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:13.943814792-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"NetworkStatus":{}}}}}
{"time":"2025-08-12T18:56:13.943818989-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_NetworkStatus); Control(local:true mailbox_slot:\"xtnzlwga4pp0\" connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:13.943848414-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:56:13.943852659-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"ztgtqevlrqd5\" connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:13.943842578-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"t3fz86c38x24","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943862231-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:56:13.943868845-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:13.943866345-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943880727-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"xtnzlwga4pp0","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943894733-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"t3fz86c38x24","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943899282-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943911684-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"xtnzlwga4pp0","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943915018-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}}}
{"time":"2025-08-12T18:56:13.943920731-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:13.943924447-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"t3fz86c38x24","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943933753-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:56:13.9439419-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:13.943948962-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}}}
{"time":"2025-08-12T18:56:13.943952465-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:13.943955274-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:13.943962852-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:56:13.943968882-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:13.94398115-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["comparison","baseline","promptmr_plus"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:56:13.943990483-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(connection_id:\"127.0.0.1:51650\")","buffer":5}
{"time":"2025-08-12T18:56:13.943884268-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"ztgtqevlrqd5","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.943998656-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944005616-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.94401151-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944016443-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944019583-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944009534-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"ztgtqevlrqd5","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:13.944024396-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["comparison","baseline","promptmr_plus"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944033901-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944039475-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944042096-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944046712-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944049182-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.944053881-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["comparison","baseline","promptmr_plus"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:13.953797832-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["requirements.txt"]}
{"time":"2025-08-12T18:56:13.95384913-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:14.********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/requirements.txt, Name: requirements.txt, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015613Z&X-Goog-Expires=86399&X-Goog-Signature=8510be5ce6a3c0cc921f1ce16d34c54ba261b427a9a2d877c06d6879371f0c6db5be6648afa1e011d2b8a30577aef4f6311e9efc9694e9d298ae58446a7d93e0be0509ada16a772aa1f227c52ee6c922f365e04ca3f55f826a3c685499def7ac49f16658a1e1f75ce3a8c53eded4d6f7ceaf4c43007ce6c1511ff29b1edcefbcf5202d3e32bf8dd3702dc3b0f57e316ba00a711273a3913a84fe3d4674bca7bca452c93c6489c764fc197c682c475ef02507768cb095702a671e9737bc4ec874cd2596cf3cf2e2753ec5fe01cb3a28b0aa062ea7c292a44cd64c5f1c1ac859125f106d9bf0376579b39b2bdc08b207dc63c33e77e3acd7a603afce190754ed12&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:56:14.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/requirements.txt","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015613Z&X-Goog-Expires=86399&X-Goog-Signature=8510be5ce6a3c0cc921f1ce16d34c54ba261b427a9a2d877c06d6879371f0c6db5be6648afa1e011d2b8a30577aef4f6311e9efc9694e9d298ae58446a7d93e0be0509ada16a772aa1f227c52ee6c922f365e04ca3f55f826a3c685499def7ac49f16658a1e1f75ce3a8c53eded4d6f7ceaf4c43007ce6c1511ff29b1edcefbcf5202d3e32bf8dd3702dc3b0f57e316ba00a711273a3913a84fe3d4674bca7bca452c93c6489c764fc197c682c475ef02507768cb095702a671e9737bc4ec874cd2596cf3cf2e2753ec5fe01cb3a28b0aa062ea7c292a44cd64c5f1c1ac859125f106d9bf0376579b39b2bdc08b207dc63c33e77e3acd7a603afce190754ed12&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:14.********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015613Z&X-Goog-Expires=86399&X-Goog-Signature=8510be5ce6a3c0cc921f1ce16d34c54ba261b427a9a2d877c06d6879371f0c6db5be6648afa1e011d2b8a30577aef4f6311e9efc9694e9d298ae58446a7d93e0be0509ada16a772aa1f227c52ee6c922f365e04ca3f55f826a3c685499def7ac49f16658a1e1f75ce3a8c53eded4d6f7ceaf4c43007ce6c1511ff29b1edcefbcf5202d3e32bf8dd3702dc3b0f57e316ba00a711273a3913a84fe3d4674bca7bca452c93c6489c764fc197c682c475ef02507768cb095702a671e9737bc4ec874cd2596cf3cf2e2753ec5fe01cb3a28b0aa062ea7c292a44cd64c5f1c1ac859125f106d9bf0376579b39b2bdc08b207dc63c33e77e3acd7a603afce190754ed12&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:14.*********-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":6,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.103871995-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:56:14.103904797-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:14.103912705-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"t3fz86c38x24","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:14.157289942-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-metadata.json"}}
{"time":"2025-08-12T18:56:14.157361195-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"uploaded\":[\"wandb-metadata.json\"]}"}
{"time":"2025-08-12T18:56:14.15738418-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/0wpkypoo/file_stream"}
{"time":"2025-08-12T18:56:14.211327601-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"requirements.txt"}}
{"time":"2025-08-12T18:56:14.251854342-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"xtnzlwga4pp0","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.251871185-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":7,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.251899233-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":8,"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.251904439-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":9,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.251954301-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":10,"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.251958448-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":11,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.251973563-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":12,"RecordType":{"Run":{"run_id":"0wpkypoo","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"bslXL_l1kspace","tags":["comparison","baseline","promptmr_plus"],"host":"esplhpc-cp088","start_time":{"seconds":1755050173,"nanos":289332000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.252024197-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:14.267893921-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:56:14.298334396-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298072000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml"}}}
{"time":"2025-08-12T18:56:14.298351752-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.298361684-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298072000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.298368838-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298072000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.339671603-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298155000},"line":"\n"}}}
{"time":"2025-08-12T18:56:14.339682765-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.339694954-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":299445000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}}}
{"time":"2025-08-12T18:56:14.339698885-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.339701907-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298155000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.339705835-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":299445000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.339709394-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298155000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.339712519-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":299445000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.351660969-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":13,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298072000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.351707792-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":14,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":298155000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.351712077-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":15,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":299445000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.355598498-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:14.797568679-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":797345000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 125 M  | train\n7 | normalized_l1    | normalized_l1_loss   | 0      | train\n8 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n111 M     Trainable params\n13.7 M    Non-trainable params\n125 M     Total params\n502.227   Total estimated model params size (MB)\n"}}}
{"time":"2025-08-12T18:56:14.797586772-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.79759322-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":797345000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 125 M  | train\n7 | normalized_l1    | normalized_l1_loss   | 0      | train\n8 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n111 M     Trainable params\n13.7 M    Non-trainable params\n125 M     Total params\n502.227   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.797601256-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":797345000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 125 M  | train\n7 | normalized_l1    | normalized_l1_loss   | 0      | train\n8 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n111 M     Trainable params\n13.7 M    Non-trainable params\n125 M     Total params\n502.227   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.797609849-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":16,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":797345000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 125 M  | train\n7 | normalized_l1    | normalized_l1_loss   | 0      | train\n8 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n111 M     Trainable params\n13.7 M    Non-trainable params\n125 M     Total params\n502.227   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.798052644-07:00","level":"DEBUG","msg":"handling record","record":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 16, \"num_adj_slices\": 7, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 15, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": true, \"pretrain_weights_path\": \"/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\", \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}}}
{"time":"2025-08-12T18:56:14.798079392-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Config); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.798088999-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 16, \"num_adj_slices\": 7, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 15, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": true, \"pretrain_weights_path\": \"/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\", \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.798119171-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 16, \"num_adj_slices\": 7, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 15, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": true, \"pretrain_weights_path\": \"/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\", \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.798137848-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":17,"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 16, \"num_adj_slices\": 7, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 15, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": true, \"pretrain_weights_path\": \"/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt\", \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.798879601-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":798780000},"line":"SLURM auto-requeueing enabled. Setting signal handlers.\n"}}}
{"time":"2025-08-12T18:56:14.798896153-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.79890196-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":798780000},"line":"SLURM auto-requeueing enabled. Setting signal handlers.\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.798918875-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":798780000},"line":"SLURM auto-requeueing enabled. Setting signal handlers.\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.798927362-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":18,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050174,"nanos":798780000},"line":"SLURM auto-requeueing enabled. Setting signal handlers.\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.801753217-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:14.808609883-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":808535000},"line":"\rSanity Checking: |          | 0/? [00:00<?, ?it/s]"}}}
{"time":"2025-08-12T18:56:14.808624425-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:14.808632186-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":808535000},"line":"\rSanity Checking: |          | 0/? [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.80864119-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":808535000},"line":"\rSanity Checking: |          | 0/? [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.808646346-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":19,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050174,"nanos":808535000},"line":"\rSanity Checking: |          | 0/? [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:14.810289158-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:14.820147823-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:23.911705673-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:56:23.911881544-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"c1htceiqc8ka\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:23.911895357-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"c1htceiqc8ka","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:23.911910133-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"c1htceiqc8ka","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:28.142945871-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142693000},"line":"\rSanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]"}}}
{"time":"2025-08-12T18:56:28.142972846-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.14298955-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142693000},"line":"\rSanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.143001638-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142693000},"line":"\rSanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.143009241-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":20,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142693000},"line":"\rSanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.147620936-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:28.184751559-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142984000},"line":"\rSanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]"}}}
{"time":"2025-08-12T18:56:28.184778643-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.184791532-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142984000},"line":"\rSanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.184804672-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142984000},"line":"\rSanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.184814629-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":21,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":142984000},"line":"\rSanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.188789-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:28.78431797-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784114000},"line":"mask:"}}}
{"time":"2025-08-12T18:56:28.784345433-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.784357814-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784228000},"line":" "}}}
{"time":"2025-08-12T18:56:28.784362074-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.784370328-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784257000},"line":"torch.Size([1, 516, 161])"}}}
{"time":"2025-08-12T18:56:28.784377945-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:28.784385111-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784276000},"line":"\n"}}}
{"time":"2025-08-12T18:56:28.784388535-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:28.784396472-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784293000},"line":"output shape:"}}}
{"time":"2025-08-12T18:56:28.784400748-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:28.784380594-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784114000},"line":"mask:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784417485-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784228000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784422597-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784257000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784426193-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784276000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.7844292-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784293000},"line":"output shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784434665-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784114000},"line":"mask:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784439494-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784228000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784442646-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784257000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784445268-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784276000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784447719-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784293000},"line":"output shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784459382-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":22,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784114000},"line":"mask:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784473705-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":23,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784228000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784476849-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":24,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784257000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784493694-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":25,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784276000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784496714-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":26,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784293000},"line":"output shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784407106-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784307000},"line":" "}}}
{"time":"2025-08-12T18:56:28.784586888-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.784597803-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784320000},"line":"torch.Size([1, 516, 161])"}}}
{"time":"2025-08-12T18:56:28.784601791-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.784608605-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784334000},"line":"\n"}}}
{"time":"2025-08-12T18:56:28.784612384-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:28.784621937-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784307000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784628004-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784320000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784631008-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784334000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784636213-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784307000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784639932-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784320000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784642638-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784334000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.78464805-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":27,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784307000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784657447-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":28,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784320000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784670718-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":29,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784334000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784872294-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784819000},"line":"\rSanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00<00:00,  1.56it/s]"}}}
{"time":"2025-08-12T18:56:28.784877674-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.784882539-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784819000},"line":"\rSanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00<00:00,  1.56it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784886103-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784819000},"line":"\rSanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00<00:00,  1.56it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.784889571-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":30,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":784819000},"line":"\rSanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00<00:00,  1.56it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.789393572-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:28.798925407-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:28.812810818-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:56:28.812831046-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050188,"nanos":812553602},"item":[{"key":"gpu.0.gpu","value_json":"10.0"},{"key":"gpu.0.memory","value_json":"4"},{"key":"gpu.0.memoryAllocated","value_json":"4.4470873845927485"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"3803185152"},{"key":"gpu.0.temp","value_json":"37.0"},{"key":"gpu.0.powerWatts","value_json":"143.166"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"20.452285714285715"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"26.0"},{"key":"gpu.1.powerWatts","value_json":"70.965"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.137857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"71.027"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.146714285714285"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"32.0"},{"key":"gpu.3.powerWatts","value_json":"71.339"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.191285714285714"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.812883411-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050188,"nanos":812553602},"item":[{"key":"gpu.0.gpu","value_json":"10.0"},{"key":"gpu.0.memory","value_json":"4"},{"key":"gpu.0.memoryAllocated","value_json":"4.4470873845927485"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"3803185152"},{"key":"gpu.0.temp","value_json":"37.0"},{"key":"gpu.0.powerWatts","value_json":"143.166"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"20.452285714285715"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"26.0"},{"key":"gpu.1.powerWatts","value_json":"70.965"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.137857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"71.027"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.146714285714285"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"32.0"},{"key":"gpu.3.powerWatts","value_json":"71.339"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.191285714285714"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.81289596-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":31,"RecordType":{"Stats":{"timestamp":{"seconds":1755050188,"nanos":812553602},"item":[{"key":"gpu.0.gpu","value_json":"10.0"},{"key":"gpu.0.memory","value_json":"4"},{"key":"gpu.0.memoryAllocated","value_json":"4.4470873845927485"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"3803185152"},{"key":"gpu.0.temp","value_json":"37.0"},{"key":"gpu.0.powerWatts","value_json":"143.166"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"20.452285714285715"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"26.0"},{"key":"gpu.1.powerWatts","value_json":"70.965"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.137857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"71.027"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.146714285714285"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"32.0"},{"key":"gpu.3.powerWatts","value_json":"71.339"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.191285714285714"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.812908337-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:56:13.289332Z","Record":{"timestamp":{"seconds":1755050188,"nanos":812553602},"item":[{"key":"gpu.0.gpu","value_json":"10.0"},{"key":"gpu.0.memory","value_json":"4"},{"key":"gpu.0.memoryAllocated","value_json":"4.4470873845927485"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"3803185152"},{"key":"gpu.0.temp","value_json":"37.0"},{"key":"gpu.0.powerWatts","value_json":"143.166"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"20.452285714285715"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"26.0"},{"key":"gpu.1.powerWatts","value_json":"70.965"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.137857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"71.027"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.146714285714285"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"32.0"},{"key":"gpu.3.powerWatts","value_json":"71.339"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.191285714285714"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}}}
{"time":"2025-08-12T18:56:28.830085035-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:56:28.830103704-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050188,"nanos":830063809},"item":[{"key":"network.recv","value_json":"6.43303859e+08"},{"key":"memory_percent","value_json":"2.4492482434622804"},{"key":"proc.memory.availableMB","value_json":"2.00213527734375e+06"},{"key":"proc.memory.rssMB","value_json":"11597.16015625"},{"key":"cpu","value_json":"1.0088013099064093"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"5.745093e+06"},{"key":"proc.memory.percent","value_json":"0.5619061734131732"},{"key":"proc.cpu.threads","value_json":"20"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.83012466-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050188,"nanos":830063809},"item":[{"key":"network.recv","value_json":"6.43303859e+08"},{"key":"memory_percent","value_json":"2.4492482434622804"},{"key":"proc.memory.availableMB","value_json":"2.00213527734375e+06"},{"key":"proc.memory.rssMB","value_json":"11597.16015625"},{"key":"cpu","value_json":"1.0088013099064093"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"5.745093e+06"},{"key":"proc.memory.percent","value_json":"0.5619061734131732"},{"key":"proc.cpu.threads","value_json":"20"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.830138091-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":32,"RecordType":{"Stats":{"timestamp":{"seconds":1755050188,"nanos":830063809},"item":[{"key":"network.recv","value_json":"6.43303859e+08"},{"key":"memory_percent","value_json":"2.4492482434622804"},{"key":"proc.memory.availableMB","value_json":"2.00213527734375e+06"},{"key":"proc.memory.rssMB","value_json":"11597.16015625"},{"key":"cpu","value_json":"1.0088013099064093"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"5.745093e+06"},{"key":"proc.memory.percent","value_json":"0.5619061734131732"},{"key":"proc.cpu.threads","value_json":"20"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.830144794-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:56:13.289332Z","Record":{"timestamp":{"seconds":1755050188,"nanos":830063809},"item":[{"key":"network.recv","value_json":"6.43303859e+08"},{"key":"memory_percent","value_json":"2.4492482434622804"},{"key":"proc.memory.availableMB","value_json":"2.00213527734375e+06"},{"key":"proc.memory.rssMB","value_json":"11597.16015625"},{"key":"cpu","value_json":"1.0088013099064093"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"5.745093e+06"},{"key":"proc.memory.percent","value_json":"0.5619061734131732"},{"key":"proc.cpu.threads","value_json":"20"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"}]}}}
{"time":"2025-08-12T18:56:28.902777799-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:56:28.902803431-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"2hxkym9fe1pi\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.902815824-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"2hxkym9fe1pi","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.902824371-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"2hxkym9fe1pi","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.902839622-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"2hxkym9fe1pi","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.902873509-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:28.940248324-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940095000},"line":"mask:"}}}
{"time":"2025-08-12T18:56:28.940265987-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.940281671-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940187000},"line":" "}}}
{"time":"2025-08-12T18:56:28.940289669-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.940298359-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940216000},"line":"torch.Size([1, 516, 161])"}}}
{"time":"2025-08-12T18:56:28.940302399-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:28.940309695-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940235000},"line":"\n"}}}
{"time":"2025-08-12T18:56:28.940314125-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:28.940320483-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940251000},"line":"output shape:"}}}
{"time":"2025-08-12T18:56:28.940323964-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:28.940329219-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940095000},"line":"mask:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940335506-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940187000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.94033847-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940216000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940341089-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940235000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940343537-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940251000},"line":"output shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940347543-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940095000},"line":"mask:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940351591-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940187000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940354084-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940216000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940357734-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940235000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.94036019-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940251000},"line":"output shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940397428-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940266000},"line":" "}}}
{"time":"2025-08-12T18:56:28.940418174-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.940433405-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940280000},"line":"torch.Size([1, 516, 161])"}}}
{"time":"2025-08-12T18:56:28.940438141-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.940445639-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940293000},"line":"\n"}}}
{"time":"2025-08-12T18:56:28.940449513-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:28.940453756-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940266000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.94045966-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940280000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940462615-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940293000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940466531-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940266000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940470342-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940280000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940473001-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940293000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940762885-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940699000},"line":"\rSanity Checking DataLoader 0: 100%|██████████| 2/2 [00:00<00:00,  2.51it/s]"}}}
{"time":"2025-08-12T18:56:28.940771044-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.940777524-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940699000},"line":"\rSanity Checking DataLoader 0: 100%|██████████| 2/2 [00:00<00:00,  2.51it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.940787788-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940699000},"line":"\rSanity Checking DataLoader 0: 100%|██████████| 2/2 [00:00<00:00,  2.51it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.962117217-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":961991000}}}}
{"time":"2025-08-12T18:56:28.962132652-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.962148006-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962085000},"line":"\r                                                                           "}}}
{"time":"2025-08-12T18:56:28.962152094-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.962155977-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":961991000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.962160983-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962085000},"line":"\r                                                                           "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.962165212-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":961991000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.962168792-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962085000},"line":"\r                                                                           "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.962197534-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962153000},"line":"\r"}}}
{"time":"2025-08-12T18:56:28.962210745-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:28.962215968-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962153000},"line":"\r"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:28.962222139-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962153000},"line":"\r"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079397436-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":33,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940095000},"line":"mask:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079442306-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":34,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940187000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079447385-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":35,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940216000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079458239-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":36,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940235000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079462003-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":37,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940251000},"line":"output shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079468595-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":38,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940266000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079471979-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":39,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940280000},"line":"torch.Size([1, 516, 161])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079482941-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":40,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940293000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079486138-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":41,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":940699000},"line":"\rSanity Checking DataLoader 0: 100%|██████████| 2/2 [00:00<00:00,  2.51it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.07945387-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"2hxkym9fe1pi","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:29.079514034-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":42,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":961991000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079517664-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":43,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962085000},"line":"\r                                                                           "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.079560747-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":44,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050188,"nanos":962153000},"line":"\r"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:29.083621047-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:29.093302349-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:29.157805024-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":0,\"content\":[\"{\\\"ts\\\":\\\"2025-08-13T01:56:14.351679\\\",\\\"content\\\":\\\"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.351714\\\",\\\"content\\\":\\\"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797622\\\",\\\"content\\\":\\\"\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797626\\\",\\\"content\\\":\\\"  | Name             | Type                 | Params | Mode\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797632\\\",\\\"content\\\":\\\"------------------------------------------------------------------\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797666\\\",\\\"content\\\":\\\"0 | NMSE             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797672\\\",\\\"content\\\":\\\"1 | SSIM             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797678\\\",\\\"content\\\":\\\"2 | PSNR             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797682\\\",\\\"content\\\":\\\"3 | ValLoss          | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797689\\\",\\\"content\\\":\\\"4 | TotExamples      | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797697\\\",\\\"content\\\":\\\"5 | TotSliceExamples | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797705\\\",\\\"content\\\":\\\"6 | promptmr         | PromptMR             | 125 M  | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797709\\\",\\\"content\\\":\\\"7 | normalized_l1    | normalized_l1_loss   | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797714\\\",\\\"content\\\":\\\"8 | loss             | SSIMLoss             | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797717\\\",\\\"content\\\":\\\"------------------------------------------------------------------\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797727\\\",\\\"content\\\":\\\"111 M     Trainable params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797730\\\",\\\"content\\\":\\\"13.7 M    Non-trainable params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797733\\\",\\\"content\\\":\\\"125 M     Total params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.797736\\\",\\\"content\\\":\\\"502.227   Total estimated model params size (MB)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:14.798931\\\",\\\"content\\\":\\\"SLURM auto-requeueing enabled. Setting signal handlers.\\\"}\",\"{\\\"ts\\\":\\\"2025-08-13T01:56:14.808651\\\",\\\"content\\\":\\\"Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00\\\\u003c?, ?it/s]mask: torch.Size([1, 516, 161])\\\"}\",\"{\\\"ts\\\":\\\"2025-08-13T01:56:28.784499\\\",\\\"content\\\":\\\"output shape: torch.Size([1, 516, 161])\\\"}\",\"{\\\"ts\\\":\\\"2025-08-13T01:56:28.784892\\\",\\\"content\\\":\\\"Sanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00\\\\u003c00:00,  1.56it/s]mask: torch.Size([1, 516, 161])\\\"}\",\"{\\\"ts\\\":\\\"2025-08-13T01:56:29.079464\\\",\\\"content\\\":\\\"output shape: torch.Size([1, 516, 161])\\\"}\",\"{\\\"ts\\\":\\\"2025-08-13T01:56:29.079489\\\",\\\"content\\\":\\\"                                                                           \\\"}\"]},\"wandb-events.jsonl\":{\"offset\":0,\"content\":[\"{\\\"system.gpu.2.smClock\\\":345,\\\"system.gpu.3.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.3.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.3.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.0.memoryAllocatedBytes\\\":3803185152,\\\"system.gpu.2.powerPercent\\\":10.146714285714285,\\\"system.gpu.0.smClock\\\":1980,\\\"system.gpu.1.memory\\\":0,\\\"system.gpu.1.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.1.powerWatts\\\":70.965,\\\"system.gpu.2.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.2.powerWatts\\\":71.027,\\\"system.gpu.0.gpu\\\":10,\\\"system.gpu.0.memoryAllocated\\\":4.4470873845927485,\\\"system.gpu.3.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.1.smClock\\\":345,\\\"system.gpu.1.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.2.memory\\\":0,\\\"system.gpu.3.temp\\\":32,\\\"_wandb\\\":true,\\\"system.gpu.0.temp\\\":37,\\\"system.gpu.0.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.0.memoryClock\\\":2619,\\\"system.gpu.0.correctedMemoryErrors\\\":0,\\\"system.gpu.2.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.2.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.3.correctedMemoryErrors\\\":0,\\\"_runtime\\\":15.523221602,\\\"system.gpu.0.powerWatts\\\":143.166,\\\"system.gpu.1.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.1.powerPercent\\\":10.137857142857143,\\\"system.gpu.1.correctedMemoryErrors\\\":0,\\\"system.gpu.2.gpu\\\":0,\\\"system.gpu.2.memoryClock\\\":2619,\\\"system.gpu.2.correctedMemoryErrors\\\":0,\\\"system.gpu.0.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.1.gpu\\\":0,\\\"system.gpu.3.memoryClock\\\":2619,\\\"system.gpu.3.gpu\\\":0,\\\"system.gpu.3.powerWatts\\\":71.339,\\\"_timestamp\\\":1.7550501888125536e+09,\\\"system.gpu.3.smClock\\\":345,\\\"system.gpu.1.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.1.temp\\\":26,\\\"system.gpu.1.memoryClock\\\":2619,\\\"system.gpu.2.temp\\\":26,\\\"system.gpu.3.powerPercent\\\":10.191285714285714,\\\"system.gpu.0.memory\\\":4,\\\"system.gpu.0.powerPercent\\\":20.452285714285715,\\\"system.gpu.2.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.3.memory\\\":0}\",\"{\\\"_timestamp\\\":1.7550501888300638e+09,\\\"system.proc.memory.availableMB\\\":2.00213527734375e+06,\\\"system.proc.memory.rssMB\\\":11597.16015625,\\\"system.network.sent\\\":5.745093e+06,\\\"system.proc.cpu.threads\\\":20,\\\"system.disk./.usagePercent\\\":1.9874434153723153,\\\"_wandb\\\":true,\\\"_runtime\\\":15.540731809,\\\"system.network.recv\\\":6.43303859e+08,\\\"system.memory_percent\\\":2.4492482434622804,\\\"system.cpu\\\":1.0088013099064093,\\\"system.disk./.usageGB\\\":17.446178436279297,\\\"system.proc.memory.percent\\\":0.5619061734131732}\"]}},\"uploaded\":[\"requirements.txt\"]}"}
{"time":"2025-08-12T18:56:29.157867836-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/0wpkypoo/file_stream"}
{"time":"2025-08-12T18:56:29.300373927-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:56:33.91068983-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:56:33.910720526-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"gpwil6jaauaf\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:33.910733609-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"gpwil6jaauaf","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:33.910747775-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"gpwil6jaauaf","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:41.623235867-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":622928000},"line":"\rTraining: |          | 0/? [00:00<?, ?it/s]"}}}
{"time":"2025-08-12T18:56:41.623413768-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:41.623426425-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":622928000},"line":"\rTraining: |          | 0/? [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.62343689-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":622928000},"line":"\rTraining: |          | 0/? [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.623444358-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":45,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":622928000},"line":"\rTraining: |          | 0/? [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.627893244-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:41.663716999-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623503000},"line":"\rTraining:   0%|          | 0/55879 [00:00<?, ?it/s]"}}}
{"time":"2025-08-12T18:56:41.66373657-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:41.663752002-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623622000},"line":"\rEpoch 0:   0%|          | 0/55879 [00:00<?, ?it/s] "}}}
{"time":"2025-08-12T18:56:41.663756448-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:41.663766609-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623503000},"line":"\rTraining:   0%|          | 0/55879 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.66380746-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623622000},"line":"\rEpoch 0:   0%|          | 0/55879 [00:00<?, ?it/s] "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.663822305-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623503000},"line":"\rTraining:   0%|          | 0/55879 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.663853662-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623622000},"line":"\rEpoch 0:   0%|          | 0/55879 [00:00<?, ?it/s] "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.663860905-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":46,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623503000},"line":"\rTraining:   0%|          | 0/55879 [00:00<?, ?it/s]"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.663879778-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":47,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":623622000},"line":"\rEpoch 0:   0%|          | 0/55879 [00:00<?, ?it/s] "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.668675534-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:41.679793909-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:41.779155536-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":778963000},"line":"mask shape:"}}}
{"time":"2025-08-12T18:56:41.779183814-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:41.779201099-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779059000},"line":" "}}}
{"time":"2025-08-12T18:56:41.7792055-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:41.779213126-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779086000},"line":"torch.Size([1, 70, 224, 87, 1])"}}}
{"time":"2025-08-12T18:56:41.779217243-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:41.779228392-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779105000},"line":"\n"}}}
{"time":"2025-08-12T18:56:41.779231979-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:41.779216198-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":778963000},"line":"mask shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779255347-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779059000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779265163-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779086000},"line":"torch.Size([1, 70, 224, 87, 1])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779264488-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":778963000},"line":"mask shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779238403-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779123000},"line":"data shape:"}}}
{"time":"2025-08-12T18:56:41.779274684-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779059000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779278978-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779086000},"line":"torch.Size([1, 70, 224, 87, 1])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.77928497-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":48,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":778963000},"line":"mask shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.77929083-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:41.779303265-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":49,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779059000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779307078-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":50,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779086000},"line":"torch.Size([1, 70, 224, 87, 1])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.77931176-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779137000},"line":" "}}}
{"time":"2025-08-12T18:56:41.779317246-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:41.779325741-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779151000},"line":"torch.Size([1, 70, 224, 87, 2])"}}}
{"time":"2025-08-12T18:56:41.779330223-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:41.779337209-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779165000},"line":"\n"}}}
{"time":"2025-08-12T18:56:41.779340645-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:41.779269123-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779105000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779363526-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779123000},"line":"data shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779366776-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779137000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779369489-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779151000},"line":"torch.Size([1, 70, 224, 87, 2])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.77937209-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779165000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779376847-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779105000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779402339-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779123000},"line":"data shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779406762-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779137000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779410077-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779151000},"line":"torch.Size([1, 70, 224, 87, 2])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.77941366-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779165000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779420563-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":51,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779105000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.77942755-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":52,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779123000},"line":"data shape:"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779440805-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":53,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779137000},"line":" "}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779443747-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":54,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779151000},"line":"torch.Size([1, 70, 224, 87, 2])"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.779456754-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":55,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":1755050201,"nanos":779165000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:41.783799414-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:41.793722326-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:43.295945046-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295662000},"line":"Traceback (most recent call last):\n"}}}
{"time":"2025-08-12T18:56:43.295972173-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.295986776-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295757000}}}}
{"time":"2025-08-12T18:56:43.295991793-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.295999815-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295843000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}}}
{"time":"2025-08-12T18:56:43.296003658-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.296010556-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295864000}}}}
{"time":"2025-08-12T18:56:43.296013716-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.296020091-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295880000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}}}
{"time":"2025-08-12T18:56:43.296023991-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.296029374-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295662000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296036168-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295757000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296039105-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295843000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296047928-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295864000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296050431-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295880000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29605521-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295662000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296059518-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295757000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296062324-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295843000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296064756-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295864000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296067233-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295880000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296072407-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":56,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295662000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296090705-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":57,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295757000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296093858-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":58,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295843000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296100051-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295895000}}}}
{"time":"2025-08-12T18:56:43.296107641-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":59,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295864000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296111177-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":60,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295880000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296124233-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.296141774-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295909000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}}}
{"time":"2025-08-12T18:56:43.296146742-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.296154039-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295928000}}}}
{"time":"2025-08-12T18:56:43.296157673-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.296164108-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295942000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296168179-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.296174333-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295955000}}}}
{"time":"2025-08-12T18:56:43.296178897-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.296185427-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295968000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}}}
{"time":"2025-08-12T18:56:43.296188746-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.296194574-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295982000}}}}
{"time":"2025-08-12T18:56:43.296197684-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":5}
{"time":"2025-08-12T18:56:43.296207486-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295994000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296211051-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":6}
{"time":"2025-08-12T18:56:43.29621877-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296009000}}}}
{"time":"2025-08-12T18:56:43.29622205-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":7}
{"time":"2025-08-12T18:56:43.296229706-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296023000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296233388-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":8}
{"time":"2025-08-12T18:56:43.296239143-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296041000}}}}
{"time":"2025-08-12T18:56:43.296242442-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":9}
{"time":"2025-08-12T18:56:43.296249007-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}}}
{"time":"2025-08-12T18:56:43.296252455-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":10}
{"time":"2025-08-12T18:56:43.296244251-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295895000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296267847-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295909000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296273359-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295928000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296275994-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295942000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296279203-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295955000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296281879-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295968000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296284782-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295982000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296292287-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295994000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296291107-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295895000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296295994-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296009000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296300326-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296023000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296299437-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295909000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296303905-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296041000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296304982-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295928000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296307186-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296308433-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295942000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296258314-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296067000}}}}
{"time":"2025-08-12T18:56:43.296312752-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295955000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296319032-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295968000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296322435-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295982000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296323709-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.296325227-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295994000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296329915-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296009000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296332361-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296023000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29633532-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296041000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296338337-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296078000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n    results = self._run_stage()\n"}}}
{"time":"2025-08-12T18:56:43.296338104-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296344044-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.29634481-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":61,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295895000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296349132-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":62,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295909000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296352064-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296089000}}}}
{"time":"2025-08-12T18:56:43.296356767-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.296365303-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296100000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n    self.fit_loop.run()\n"}}}
{"time":"2025-08-12T18:56:43.296369307-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.296375867-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296111000}}}}
{"time":"2025-08-12T18:56:43.296379463-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.296385623-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296121000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n    self.advance()\n"}}}
{"time":"2025-08-12T18:56:43.296387832-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":63,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295928000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296391704-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296067000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29639608-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296078000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n    results = self._run_stage()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296399002-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296089000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296401405-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296100000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n    self.fit_loop.run()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296404235-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296111000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296411322-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296067000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296392064-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":64,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295942000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296445103-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":65,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295955000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296448612-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":66,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295968000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296389703-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.296466711-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296132000}}}}
{"time":"2025-08-12T18:56:43.296472376-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.296481312-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296143000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n    self.epoch_loop.run(self._data_fetcher)\n"}}}
{"time":"2025-08-12T18:56:43.29648621-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.296494578-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296154000}}}}
{"time":"2025-08-12T18:56:43.296495306-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":67,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295982000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296498284-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.296498524-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":68,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":295994000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296505868-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296165000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n    self.advance(data_fetcher)\n"}}}
{"time":"2025-08-12T18:56:43.296511957-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.296518029-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296178000}}}}
{"time":"2025-08-12T18:56:43.296521369-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.296529558-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296190000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n    batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.29653378-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":5}
{"time":"2025-08-12T18:56:43.296539835-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296202000}}}}
{"time":"2025-08-12T18:56:43.296543441-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":6}
{"time":"2025-08-12T18:56:43.296550427-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296213000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n    self._optimizer_step(batch_idx, closure)\n"}}}
{"time":"2025-08-12T18:56:43.296550709-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":69,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296009000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296554195-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":7}
{"time":"2025-08-12T18:56:43.296554506-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":70,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296023000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296561004-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296225000}}}}
{"time":"2025-08-12T18:56:43.296564608-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":8}
{"time":"2025-08-12T18:56:43.296570701-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296236000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n    call._call_lightning_module_hook(\n"}}}
{"time":"2025-08-12T18:56:43.2965748-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":9}
{"time":"2025-08-12T18:56:43.296580736-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296246000}}}}
{"time":"2025-08-12T18:56:43.296415149-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296078000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n    results = self._run_stage()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296586638-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":10}
{"time":"2025-08-12T18:56:43.296589198-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296089000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296594031-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296257000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n    output = fn(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296594642-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296100000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n    self.fit_loop.run()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296597873-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":11}
{"time":"2025-08-12T18:56:43.296599637-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296111000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296606492-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296268000}}}}
{"time":"2025-08-12T18:56:43.296610158-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":12}
{"time":"2025-08-12T18:56:43.296610045-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296121000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n    self.advance()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296613758-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296132000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296622663-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296281000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n    optimizer.step(closure=optimizer_closure)\n"}}}
{"time":"2025-08-12T18:56:43.296624636-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296121000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n    self.advance()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296631565-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296132000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296596178-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":71,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296041000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296641736-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":72,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296621513-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296143000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n    self.epoch_loop.run(self._data_fetcher)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296673106-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296154000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296677182-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296165000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n    self.advance(data_fetcher)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296680686-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296178000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296683584-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296190000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n    batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296686825-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296202000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296689698-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296213000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n    self._optimizer_step(batch_idx, closure)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29669523-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296225000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296627411-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":11}
{"time":"2025-08-12T18:56:43.296702754-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296143000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n    self.epoch_loop.run(self._data_fetcher)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296708214-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296295000}}}}
{"time":"2025-08-12T18:56:43.296691171-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":73,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296067000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29671316-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.29671433-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":74,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296078000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n    results = self._run_stage()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296698478-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296236000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n    call._call_lightning_module_hook(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296719434-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296246000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296720917-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n    step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296722257-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296257000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n    output = fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296725574-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.296725806-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296268000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296730398-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296281000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n    optimizer.step(closure=optimizer_closure)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296732385-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296318000}}}}
{"time":"2025-08-12T18:56:43.296733643-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296295000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296736574-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.296737306-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n    step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296741115-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296318000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296707779-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296154000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296743357-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296330000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n    optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296745346-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296165000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n    self.advance(data_fetcher)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296747686-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":75,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296089000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296749379-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296178000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296751337-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":76,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296100000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n    self.fit_loop.run()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296781871-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":77,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296111000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296785161-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":78,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296121000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n    self.advance()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296805812-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":79,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296132000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296808769-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":80,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296143000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n    self.epoch_loop.run(self._data_fetcher)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296752159-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296190000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n    batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29682861-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296202000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296832854-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":81,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296154000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296833717-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296213000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n    self._optimizer_step(batch_idx, closure)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296836517-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":82,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296165000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n    self.advance(data_fetcher)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296837777-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296225000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296844656-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296236000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n    call._call_lightning_module_hook(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296847913-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296246000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.2968507-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296257000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n    output = fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296853681-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296268000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296856282-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296281000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n    optimizer.step(closure=optimizer_closure)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296859095-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296295000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296859538-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":83,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296178000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296862761-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n    step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296867557-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296318000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296864243-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":84,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296190000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n    batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29694275-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":85,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296202000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296747189-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.296946344-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":86,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296213000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n    self._optimizer_step(batch_idx, closure)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296958451-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296344000}}}}
{"time":"2025-08-12T18:56:43.296963546-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.29697689-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296355000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n    return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.296981461-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.296987704-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296366000}}}}
{"time":"2025-08-12T18:56:43.296990881-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.296990644-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":87,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296225000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296994061-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":88,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296236000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n    call._call_lightning_module_hook(\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.296997862-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296378000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n    return optimizer.step(closure=closure, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.29700174-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.297008653-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296392000}}}}
{"time":"2025-08-12T18:56:43.297013297-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.297020065-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296403000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n    out = func(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297023489-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":5}
{"time":"2025-08-12T18:56:43.297028775-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":89,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296246000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29703027-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296416000}}}}
{"time":"2025-08-12T18:56:43.2970326-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":90,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296257000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n    output = fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297035293-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":6}
{"time":"2025-08-12T18:56:43.297041916-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296428000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n    ret = func(self, *args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297045982-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":7}
{"time":"2025-08-12T18:56:43.297051739-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296440000}}}}
{"time":"2025-08-12T18:56:43.297054909-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":8}
{"time":"2025-08-12T18:56:43.297061736-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296451000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n    loss = closure()\n"}}}
{"time":"2025-08-12T18:56:43.297063115-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":91,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296268000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297065637-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":9}
{"time":"2025-08-12T18:56:43.297066565-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":92,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296281000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n    optimizer.step(closure=optimizer_closure)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297072085-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296463000}}}}
{"time":"2025-08-12T18:56:43.297075834-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":10}
{"time":"2025-08-12T18:56:43.29708208-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296474000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n    closure_result = closure()\n"}}}
{"time":"2025-08-12T18:56:43.297085673-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":11}
{"time":"2025-08-12T18:56:43.297091658-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296484000}}}}
{"time":"2025-08-12T18:56:43.297267674-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":12}
{"time":"2025-08-12T18:56:43.297276808-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296330000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n    optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297287792-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296344000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297297526-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296330000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n    optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297298283-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":93,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296295000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29730233-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296344000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297304372-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":94,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296306000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n    step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297291835-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296355000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n    return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297333739-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296366000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297341514-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296378000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n    return optimizer.step(closure=closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297344614-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296392000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297347168-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296403000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n    out = func(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297350107-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296416000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297349917-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296494000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n    self._result = self.closure(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297352568-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296428000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n    ret = func(self, *args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297356333-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296440000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297358148-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.297359725-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296451000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n    loss = closure()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297363237-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296463000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297367192-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296505000}}}}
{"time":"2025-08-12T18:56:43.297367807-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296474000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n    closure_result = closure()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297375683-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.297378673-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296355000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n    return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297384295-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296366000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297384969-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296515000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n    return func(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297386957-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296378000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n    return optimizer.step(closure=closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297390601-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.297374694-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296484000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297403884-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296526000}}}}
{"time":"2025-08-12T18:56:43.297400587-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296494000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n    self._result = self.closure(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297408476-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.297408055-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296505000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297415989-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296515000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n    return func(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297418003-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296536000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n    step_output = self._step_fn()\n"}}}
{"time":"2025-08-12T18:56:43.297419856-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296526000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297425895-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.297433614-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296547000}}}}
{"time":"2025-08-12T18:56:43.297437628-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.297448478-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296558000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n    training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n"}}}
{"time":"2025-08-12T18:56:43.297452814-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.297459858-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296571000}}}}
{"time":"2025-08-12T18:56:43.297463613-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.297473316-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296583000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n    output = fn(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297477849-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.297484384-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296595000}}}}
{"time":"2025-08-12T18:56:43.297490292-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.297498871-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296606000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n    return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297504706-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":5}
{"time":"2025-08-12T18:56:43.297513101-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296622000}}}}
{"time":"2025-08-12T18:56:43.297516247-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":6}
{"time":"2025-08-12T18:56:43.297523032-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296633000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n    wrapper_output = wrapper_module(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297527927-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":7}
{"time":"2025-08-12T18:56:43.297531028-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":95,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296318000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297536977-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296645000}}}}
{"time":"2025-08-12T18:56:43.297536119-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":96,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296330000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n    optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297541733-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":8}
{"time":"2025-08-12T18:56:43.297549371-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296655000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297555582-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":9}
{"time":"2025-08-12T18:56:43.297561883-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296665000}}}}
{"time":"2025-08-12T18:56:43.297565036-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":10}
{"time":"2025-08-12T18:56:43.297574163-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296675000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.29757835-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":11}
{"time":"2025-08-12T18:56:43.297580073-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":97,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296344000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297585278-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296687000}}}}
{"time":"2025-08-12T18:56:43.297587053-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":98,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296355000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n    return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297591706-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":12}
{"time":"2025-08-12T18:56:43.297600233-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296697000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n    else self._run_ddp_forward(*inputs, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297604243-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":13}
{"time":"2025-08-12T18:56:43.297621218-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296707000}}}}
{"time":"2025-08-12T18:56:43.297624739-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":14}
{"time":"2025-08-12T18:56:43.297628019-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296536000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n    step_output = self._step_fn()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297631418-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296547000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297633927-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296558000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n    training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297636618-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296571000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297638122-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":99,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296366000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297639228-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296583000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n    output = fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297647359-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296595000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29739022-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296392000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297650089-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296606000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n    return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297652412-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296403000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n    out = func(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297653535-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296622000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297655915-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296416000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297656776-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296633000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n    wrapper_output = wrapper_module(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297660065-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296645000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297659071-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296428000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n    ret = func(self, *args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297666208-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296440000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297670829-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296451000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n    loss = closure()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297641445-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":100,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296378000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n    return optimizer.step(closure=closure, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297729233-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":101,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296392000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29773343-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":102,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296403000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n    out = func(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297762178-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":103,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296416000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297766579-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":104,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296428000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n    ret = func(self, *args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297790703-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296719000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n    return self.module(*inputs, **kwargs)  # type: ignore[index]\n"}}}
{"time":"2025-08-12T18:56:43.297795032-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":105,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296440000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297798595-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":106,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296451000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n    loss = closure()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297800118-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":5}
{"time":"2025-08-12T18:56:43.297812583-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296730000}}}}
{"time":"2025-08-12T18:56:43.297816825-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":6}
{"time":"2025-08-12T18:56:43.297825782-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296741000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.29766238-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296655000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297831832-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":7}
{"time":"2025-08-12T18:56:43.297832071-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296665000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297835101-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296675000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297837795-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296687000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297839003-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296752000}}}}
{"time":"2025-08-12T18:56:43.297843229-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.297840136-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296697000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n    else self._run_ddp_forward(*inputs, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297846211-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296707000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297848531-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296719000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n    return self.module(*inputs, **kwargs)  # type: ignore[index]\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297851597-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296730000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297852393-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296765000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297855963-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296741000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297859104-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.297860033-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296752000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297863138-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296765000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297675503-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296463000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297868321-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296776000}}}}
{"time":"2025-08-12T18:56:43.297873704-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":107,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296463000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297868323-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296474000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n    closure_result = closure()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297880051-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296484000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297875194-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.297883682-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296494000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n    self._result = self.closure(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297890555-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296505000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297893423-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296776000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297898072-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":108,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296474000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n    closure_result = closure()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297893956-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296515000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n    return func(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297904941-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296526000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297908071-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296536000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n    step_output = self._step_fn()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297910983-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296547000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297913338-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296558000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n    training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297915889-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296571000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297918319-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296583000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n    output = fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297920921-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296595000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297926287-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296606000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n    return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297931592-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296622000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297934031-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296633000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n    wrapper_output = wrapper_module(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297936691-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296645000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297939998-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296655000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297942941-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296665000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297945368-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296675000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297947998-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296687000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297950296-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296697000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n    else self._run_ddp_forward(*inputs, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297956453-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296707000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297960757-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296719000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n    return self.module(*inputs, **kwargs)  # type: ignore[index]\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29796372-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296730000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29796616-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296741000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297969022-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296752000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297971471-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296765000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297974123-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296776000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29795314-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":109,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296484000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297979703-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":110,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296494000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n    self._result = self.closure(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.297890984-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296787000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n    out = method(*_args, **_kwargs)\n"}}}
{"time":"2025-08-12T18:56:43.297991372-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.297999982-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296797000}}}}
{"time":"2025-08-12T18:56:43.298003763-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.298015049-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296808000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n    pred_kspace=output_dict['pred_kspace']\n"}}}
{"time":"2025-08-12T18:56:43.298018858-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":1}
{"time":"2025-08-12T18:56:43.298021551-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":111,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296505000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298025086-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296820000}}}}
{"time":"2025-08-12T18:56:43.298025492-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":112,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296515000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n    return func(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298031984-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":2}
{"time":"2025-08-12T18:56:43.298038862-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296837000},"line":"KeyError: 'pred_kspace'\n"}}}
{"time":"2025-08-12T18:56:43.298043685-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":3}
{"time":"2025-08-12T18:56:43.29805235-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296851000}}}}
{"time":"2025-08-12T18:56:43.298055832-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":4}
{"time":"2025-08-12T18:56:43.298058821-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296787000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n    out = method(*_args, **_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298058954-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":113,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296526000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29806514-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":114,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296536000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n    step_output = self._step_fn()\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298065252-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296787000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n    out = method(*_args, **_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298111525-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":115,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296547000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298118532-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":116,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296558000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n    training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298062353-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296797000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298141293-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296808000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n    pred_kspace=output_dict['pred_kspace']\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298145209-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296820000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298148121-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296837000},"line":"KeyError: 'pred_kspace'\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298151381-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296851000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298154933-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296797000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298161196-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296808000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n    pred_kspace=output_dict['pred_kspace']\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298164191-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296820000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298166902-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296837000},"line":"KeyError: 'pred_kspace'\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298172041-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296851000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298168241-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":117,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296571000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298177679-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":118,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296583000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n    output = fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298223538-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":119,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296595000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298226961-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":120,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296606000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n    return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298274232-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":121,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296622000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298276916-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":122,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296633000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n    wrapper_output = wrapper_module(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298310441-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":123,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296645000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298312972-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":124,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296655000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298348561-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":125,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296665000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298351295-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":126,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296675000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29838238-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":127,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296687000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298385283-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":128,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296697000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n    else self._run_ddp_forward(*inputs, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298417011-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":129,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296707000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298420116-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":130,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296719000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n    return self.module(*inputs, **kwargs)  # type: ignore[index]\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298457358-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":131,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296730000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298459835-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":132,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296741000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298492771-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":133,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296752000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298497265-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":134,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296765000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.298846598-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":135,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296776000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.29886179-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":136,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296787000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n    out = method(*_args, **_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.299107181-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":137,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296797000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.299111318-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":138,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296808000},"line":"  File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n    pred_kspace=output_dict['pred_kspace']\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.299144981-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":139,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296820000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.299148003-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":140,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296837000},"line":"KeyError: 'pred_kspace'\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.299154106-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":141,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":296851000}}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.300160529-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:43.306726739-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":306586000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n[rank0]:     results = self._run_stage()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n[rank0]:     self.fit_loop.run()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n[rank0]:     self.advance()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n[rank0]:     self.epoch_loop.run(self._data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n[rank0]:     self.advance(data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n[rank0]:     batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n[rank0]:     self._optimizer_step(batch_idx, closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n[rank0]:     call._call_lightning_module_hook(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n[rank0]:     optimizer.step(closure=optimizer_closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n[rank0]:     step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n[rank0]:     optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n[rank0]:     return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n[rank0]:     return optimizer.step(closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n[rank0]:     out = func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n[rank0]:     ret = func(self, *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n[rank0]:     loss = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n[rank0]:     closure_result = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n[rank0]:     self._result = self.closure(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n[rank0]:     return func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n[rank0]:     step_output = self._step_fn()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n[rank0]:     training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n[rank0]:     return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n[rank0]:     wrapper_output = wrapper_module(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n[rank0]:     out = method(*_args, **_kwargs)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n[rank0]:     pred_kspace=output_dict['pred_kspace']\n[rank0]: KeyError: 'pred_kspace'\n"}}}
{"time":"2025-08-12T18:56:43.306748886-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.306755074-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":306586000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n[rank0]:     results = self._run_stage()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n[rank0]:     self.fit_loop.run()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n[rank0]:     self.advance()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n[rank0]:     self.epoch_loop.run(self._data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n[rank0]:     self.advance(data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n[rank0]:     batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n[rank0]:     self._optimizer_step(batch_idx, closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n[rank0]:     call._call_lightning_module_hook(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n[rank0]:     optimizer.step(closure=optimizer_closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n[rank0]:     step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n[rank0]:     optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n[rank0]:     return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n[rank0]:     return optimizer.step(closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n[rank0]:     out = func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n[rank0]:     ret = func(self, *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n[rank0]:     loss = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n[rank0]:     closure_result = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n[rank0]:     self._result = self.closure(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n[rank0]:     return func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n[rank0]:     step_output = self._step_fn()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n[rank0]:     training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n[rank0]:     return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n[rank0]:     wrapper_output = wrapper_module(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n[rank0]:     out = method(*_args, **_kwargs)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n[rank0]:     pred_kspace=output_dict['pred_kspace']\n[rank0]: KeyError: 'pred_kspace'\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.306776583-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":306586000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n[rank0]:     results = self._run_stage()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n[rank0]:     self.fit_loop.run()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n[rank0]:     self.advance()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n[rank0]:     self.epoch_loop.run(self._data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n[rank0]:     self.advance(data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n[rank0]:     batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n[rank0]:     self._optimizer_step(batch_idx, closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n[rank0]:     call._call_lightning_module_hook(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n[rank0]:     optimizer.step(closure=optimizer_closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n[rank0]:     step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n[rank0]:     optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n[rank0]:     return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n[rank0]:     return optimizer.step(closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n[rank0]:     out = func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n[rank0]:     ret = func(self, *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n[rank0]:     loss = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n[rank0]:     closure_result = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n[rank0]:     self._result = self.closure(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n[rank0]:     return func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n[rank0]:     step_output = self._step_fn()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n[rank0]:     training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n[rank0]:     return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n[rank0]:     wrapper_output = wrapper_module(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n[rank0]:     out = method(*_args, **_kwargs)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n[rank0]:     pred_kspace=output_dict['pred_kspace']\n[rank0]: KeyError: 'pred_kspace'\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.306796224-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":142,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755050203,"nanos":306586000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 986, in _run\n[rank0]:     results = self._run_stage()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 1030, in _run_stage\n[rank0]:     self.fit_loop.run()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 205, in run\n[rank0]:     self.advance()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\", line 363, in advance\n[rank0]:     self.epoch_loop.run(self._data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 140, in run\n[rank0]:     self.advance(data_fetcher)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\", line 250, in advance\n[rank0]:     batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 190, in run\n[rank0]:     self._optimizer_step(batch_idx, closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 268, in _optimizer_step\n[rank0]:     call._call_lightning_module_hook(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 159, in _call_lightning_module_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\", line 1308, in optimizer_step\n[rank0]:     optimizer.step(closure=optimizer_closure)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\", line 153, in step\n[rank0]:     step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\", line 270, in optimizer_step\n[rank0]:     optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 238, in optimizer_step\n[rank0]:     return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 122, in optimizer_step\n[rank0]:     return optimizer.step(closure=closure, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 484, in wrapper\n[rank0]:     out = func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 89, in _use_grad\n[rank0]:     ret = func(self, *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\", line 204, in step\n[rank0]:     loss = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\", line 108, in _wrap_closure\n[rank0]:     closure_result = closure()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 144, in __call__\n[rank0]:     self._result = self.closure(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\", line 116, in decorate_context\n[rank0]:     return func(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 129, in closure\n[rank0]:     step_output = self._step_fn()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\", line 317, in _training_step\n[rank0]:     training_step_output = call._call_strategy_hook(trainer, \"training_step\", *kwargs.values())\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 311, in _call_strategy_hook\n[rank0]:     output = fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 389, in training_step\n[rank0]:     return self._forward_redirection(self.model, self.lightning_module, \"training_step\", *args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 640, in __call__\n[rank0]:     wrapper_output = wrapper_module(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1636, in forward\n[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\", line 1454, in _run_ddp_forward\n[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1553, in _wrapped_call_impl\n[rank0]:     return self._call_impl(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\", line 1562, in _call_impl\n[rank0]:     return forward_call(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 633, in wrapped_forward\n[rank0]:     out = method(*_args, **_kwargs)\n[rank0]:   File \"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\", line 254, in training_step\n[rank0]:     pred_kspace=output_dict['pred_kspace']\n[rank0]: KeyError: 'pred_kspace'\n"}},"control":{"connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.309352594-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:43.31918829-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:56:43.813172704-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:56:43.813192315-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050203,"nanos":813002338},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"8.588874311847865"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"7345274880"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.724"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.103428571428573"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"25.0"},{"key":"gpu.1.powerWatts","value_json":"70.972"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.138857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"70.983"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.140428571428572"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"31.0"},{"key":"gpu.3.powerWatts","value_json":"71.296"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.185142857142857"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.813233177-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050203,"nanos":813002338},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"8.588874311847865"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"7345274880"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.724"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.103428571428573"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"25.0"},{"key":"gpu.1.powerWatts","value_json":"70.972"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.138857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"70.983"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.140428571428572"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"31.0"},{"key":"gpu.3.powerWatts","value_json":"71.296"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.185142857142857"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.813259464-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":143,"RecordType":{"Stats":{"timestamp":{"seconds":1755050203,"nanos":813002338},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"8.588874311847865"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"7345274880"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.724"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.103428571428573"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"25.0"},{"key":"gpu.1.powerWatts","value_json":"70.972"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.138857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"70.983"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.140428571428572"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"31.0"},{"key":"gpu.3.powerWatts","value_json":"71.296"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.185142857142857"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.813279085-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:56:13.289332Z","Record":{"timestamp":{"seconds":1755050203,"nanos":813002338},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"8.588874311847865"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"7345274880"},{"key":"gpu.0.temp","value_json":"35.0"},{"key":"gpu.0.powerWatts","value_json":"112.724"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"16.103428571428573"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"25.0"},{"key":"gpu.1.powerWatts","value_json":"70.972"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.138857142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"26.0"},{"key":"gpu.2.powerWatts","value_json":"70.983"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.140428571428572"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"31.0"},{"key":"gpu.3.powerWatts","value_json":"71.296"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.185142857142857"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}}}
{"time":"2025-08-12T18:56:43.827524322-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:56:43.82754365-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050203,"nanos":827504891},"item":[{"key":"memory_percent","value_json":"2.3766931925800296"},{"key":"proc.memory.rssMB","value_json":"11842.2265625"},{"key":"proc.memory.percent","value_json":"0.5737801429637138"},{"key":"proc.cpu.threads","value_json":"16"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"},{"key":"network.recv","value_json":"1.485567265e+09"},{"key":"proc.memory.availableMB","value_json":"2.00378371875e+06"},{"key":"cpu","value_json":"0.9880928849217431"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"1.4530097e+07"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.827562947-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755050203,"nanos":827504891},"item":[{"key":"memory_percent","value_json":"2.3766931925800296"},{"key":"proc.memory.rssMB","value_json":"11842.2265625"},{"key":"proc.memory.percent","value_json":"0.5737801429637138"},{"key":"proc.cpu.threads","value_json":"16"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"},{"key":"network.recv","value_json":"1.485567265e+09"},{"key":"proc.memory.availableMB","value_json":"2.00378371875e+06"},{"key":"cpu","value_json":"0.9880928849217431"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"1.4530097e+07"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.827571133-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":144,"RecordType":{"Stats":{"timestamp":{"seconds":1755050203,"nanos":827504891},"item":[{"key":"memory_percent","value_json":"2.3766931925800296"},{"key":"proc.memory.rssMB","value_json":"11842.2265625"},{"key":"proc.memory.percent","value_json":"0.5737801429637138"},{"key":"proc.cpu.threads","value_json":"16"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"},{"key":"network.recv","value_json":"1.485567265e+09"},{"key":"proc.memory.availableMB","value_json":"2.00378371875e+06"},{"key":"cpu","value_json":"0.9880928849217431"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"1.4530097e+07"}]}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.827576534-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:56:13.289332Z","Record":{"timestamp":{"seconds":1755050203,"nanos":827504891},"item":[{"key":"memory_percent","value_json":"2.3766931925800296"},{"key":"proc.memory.rssMB","value_json":"11842.2265625"},{"key":"proc.memory.percent","value_json":"0.5737801429637138"},{"key":"proc.cpu.threads","value_json":"16"},{"key":"disk./.usagePercent","value_json":"1.9874434153723153"},{"key":"network.recv","value_json":"1.485567265e+09"},{"key":"proc.memory.availableMB","value_json":"2.00378371875e+06"},{"key":"cpu","value_json":"0.9880928849217431"},{"key":"disk./.usageGB","value_json":"17.446178436279297"},{"key":"network.sent","value_json":"1.4530097e+07"}]}}}
{"time":"2025-08-12T18:56:43.902789704-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:56:43.902805793-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"0t4kwdwtu125\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.902811952-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"0t4kwdwtu125","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.902818149-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"0t4kwdwtu125","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.902822128-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"0t4kwdwtu125","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.9028516-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:43.910584686-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:56:43.910600444-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"bctxm1she106\" connection_id:\"127.0.0.1:51650\")","buffer":0}
{"time":"2025-08-12T18:56:43.910609543-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"bctxm1she106","connection_id":"127.0.0.1:51650"},"_info":{"stream_id":"0wpkypoo"}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.91062671-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"bctxm1she106","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:43.911599755-07:00","level":"DEBUG","msg":"handling record","record":{"Exit":{"exit_code":1}}}
{"time":"2025-08-12T18:56:43.911638166-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Exit); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:56:43.911641376-07:00","level":"INFO","msg":"stream: closing","id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.911643833-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.911652847-07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-12T18:56:43.91166647-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"30"}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:43.91167946-07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-12T18:56:43.91168654-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1,"runtime":30}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070503666-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":145,"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"30"}]}}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070526253-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:56:44.070531466-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Record":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"30"}]}}}
{"time":"2025-08-12T18:56:44.070544216-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":146,"RecordType":{"Exit":{"exit_code":1,"runtime":30}},"control":{"always_send":true}}},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070569342-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(1))","buffer":0}
{"time":"2025-08-12T18:56:44.070574594-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070582885-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070586122-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070562482-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"0t4kwdwtu125","connection_id":"127.0.0.1:51650"}}}
{"time":"2025-08-12T18:56:44.070594547-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(2))","buffer":0}
{"time":"2025-08-12T18:56:44.070602851-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070605268-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.070612821-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.075010009-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:56:44.075083111-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:44.120820258-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["output.log","wandb-summary.json"]}
{"time":"2025-08-12T18:56:44.120919329-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:44.158042979-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":24,\"content\":[\"{\\\"ts\\\":\\\"2025-08-13T01:56:29.079489\\\",\\\"content\\\":\\\"Epoch 0:   0%|          | 0/55879 [00:00\\\\u003c?, ?it/s] mask shape: torch.Size([1, 70, 224, 87, 1])\\\"}\",\"{\\\"ts\\\":\\\"2025-08-13T01:56:41.779435\\\",\\\"content\\\":\\\"data shape: torch.Size([1, 70, 224, 87, 2])\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296077\\\",\\\"content\\\":\\\"Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296096\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296105\\\",\\\"content\\\":\\\"    run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296115\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296120\\\",\\\"content\\\":\\\"    cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296353\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296380\\\",\\\"content\\\":\\\"    self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296418\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296441\\\",\\\"content\\\":\\\"    fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296452\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296490\\\",\\\"content\\\":\\\"    call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296501\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296534\\\",\\\"content\\\":\\\"    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296557\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296589\\\",\\\"content\\\":\\\"    return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296645\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296682\\\",\\\"content\\\":\\\"    self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296717\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 986, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296740\\\",\\\"content\\\":\\\"    results = self._run_stage()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296756\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 1030, in _run_stage\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296778\\\",\\\"content\\\":\\\"    self.fit_loop.run()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296787\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\\\\\\\", line 205, in run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296803\\\",\\\"content\\\":\\\"    self.advance()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296811\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\\\\\\\", line 363, in advance\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296827\\\",\\\"content\\\":\\\"    self.epoch_loop.run(self._data_fetcher)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296839\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\\\\\\\", line 140, in run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296856\\\",\\\"content\\\":\\\"    self.advance(data_fetcher)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296875\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\\\\\\\", line 250, in advance\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296914\\\",\\\"content\\\":\\\"    batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296954\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 190, in run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296984\\\",\\\"content\\\":\\\"    self._optimizer_step(batch_idx, closure)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.296996\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 268, in _optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297022\\\",\\\"content\\\":\\\"    call._call_lightning_module_hook(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297035\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 159, in _call_lightning_module_hook\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297059\\\",\\\"content\\\":\\\"    output = fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297071\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\\\\\\\", line 1308, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297280\\\",\\\"content\\\":\\\"    optimizer.step(closure=optimizer_closure)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297307\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\\\\\\\", line 153, in step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297515\\\",\\\"content\\\":\\\"    step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297541\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\\\\\\\", line 270, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297567\\\",\\\"content\\\":\\\"    optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297590\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 238, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297614\\\",\\\"content\\\":\\\"    return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297678\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\\\\\\\", line 122, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297717\\\",\\\"content\\\":\\\"    return optimizer.step(closure=closure, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297736\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 484, in wrapper\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297757\\\",\\\"content\\\":\\\"    out = func(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297768\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 89, in _use_grad\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297787\\\",\\\"content\\\":\\\"    ret = func(self, *args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297801\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\\\\\\\", line 204, in step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297821\\\",\\\"content\\\":\\\"    loss = closure()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297902\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\\\\\\\", line 108, in _wrap_closure\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297944\\\",\\\"content\\\":\\\"    closure_result = closure()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.297985\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 144, in __call__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298013\\\",\\\"content\\\":\\\"    self._result = self.closure(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298028\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\\\\\\\", line 116, in decorate_context\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298053\\\",\\\"content\\\":\\\"    return func(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298067\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 129, in closure\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298106\\\",\\\"content\\\":\\\"    step_output = self._step_fn()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298121\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 317, in _training_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298150\\\",\\\"content\\\":\\\"    training_step_output = call._call_strategy_hook(trainer, \\\\\\\"training_step\\\\\\\", *kwargs.values())\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298181\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 311, in _call_strategy_hook\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298215\\\",\\\"content\\\":\\\"    output = fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298229\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 389, in training_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298255\\\",\\\"content\\\":\\\"    return self._forward_redirection(self.model, self.lightning_module, \\\\\\\"training_step\\\\\\\", *args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298279\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 640, in __call__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298301\\\",\\\"content\\\":\\\"    wrapper_output = wrapper_module(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298315\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1553, in _wrapped_call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298340\\\",\\\"content\\\":\\\"    return self._call_impl(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298355\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1562, in _call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298375\\\",\\\"content\\\":\\\"    return forward_call(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298387\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\\\\\\\", line 1636, in forward\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298408\\\",\\\"content\\\":\\\"    else self._run_ddp_forward(*inputs, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298422\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\\\\\\\", line 1454, in _run_ddp_forward\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298448\\\",\\\"content\\\":\\\"    return self.module(*inputs, **kwargs)  # type: ignore[index]\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298462\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1553, in _wrapped_call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298486\\\",\\\"content\\\":\\\"    return self._call_impl(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298499\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1562, in _call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.298826\\\",\\\"content\\\":\\\"    return forward_call(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.299059\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 633, in wrapped_forward\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.299097\\\",\\\"content\\\":\\\"    out = method(*_args, **_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.299114\\\",\\\"content\\\":\\\"  File \\\\\\\"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\\\\\\\", line 254, in training_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.299138\\\",\\\"content\\\":\\\"    pred_kspace=output_dict['pred_kspace']\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.299150\\\",\\\"content\\\":\\\"KeyError: 'pred_kspace'\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306813\\\",\\\"content\\\":\\\"[rank0]: Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306823\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306832\\\",\\\"content\\\":\\\"[rank0]:     run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306834\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306841\\\",\\\"content\\\":\\\"[rank0]:     cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306845\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306873\\\",\\\"content\\\":\\\"[rank0]:     self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306879\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306902\\\",\\\"content\\\":\\\"[rank0]:     fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306906\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306928\\\",\\\"content\\\":\\\"[rank0]:     call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306932\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306955\\\",\\\"content\\\":\\\"[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306965\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306987\\\",\\\"content\\\":\\\"[rank0]:     return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.306992\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307014\\\",\\\"content\\\":\\\"[rank0]:     self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307019\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 986, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307036\\\",\\\"content\\\":\\\"[rank0]:     results = self._run_stage()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307039\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 1030, in _run_stage\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307056\\\",\\\"content\\\":\\\"[rank0]:     self.fit_loop.run()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307059\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\\\\\\\", line 205, in run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307074\\\",\\\"content\\\":\\\"[rank0]:     self.advance()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307076\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py\\\\\\\", line 363, in advance\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307094\\\",\\\"content\\\":\\\"[rank0]:     self.epoch_loop.run(self._data_fetcher)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307099\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\\\\\\\", line 140, in run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307116\\\",\\\"content\\\":\\\"[rank0]:     self.advance(data_fetcher)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307121\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py\\\\\\\", line 250, in advance\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307139\\\",\\\"content\\\":\\\"[rank0]:     batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307149\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 190, in run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307165\\\",\\\"content\\\":\\\"[rank0]:     self._optimizer_step(batch_idx, closure)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307171\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 268, in _optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307190\\\",\\\"content\\\":\\\"[rank0]:     call._call_lightning_module_hook(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307195\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 159, in _call_lightning_module_hook\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307212\\\",\\\"content\\\":\\\"[rank0]:     output = fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307216\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py\\\\\\\", line 1308, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307231\\\",\\\"content\\\":\\\"[rank0]:     optimizer.step(closure=optimizer_closure)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307236\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py\\\\\\\", line 153, in step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307253\\\",\\\"content\\\":\\\"[rank0]:     step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307261\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py\\\\\\\", line 270, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307277\\\",\\\"content\\\":\\\"[rank0]:     optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307287\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 238, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307309\\\",\\\"content\\\":\\\"[rank0]:     return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307319\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\\\\\\\", line 122, in optimizer_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307336\\\",\\\"content\\\":\\\"[rank0]:     return optimizer.step(closure=closure, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307342\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 484, in wrapper\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307360\\\",\\\"content\\\":\\\"[rank0]:     out = func(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307363\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 89, in _use_grad\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307378\\\",\\\"content\\\":\\\"[rank0]:     ret = func(self, *args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307382\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py\\\\\\\", line 204, in step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307393\\\",\\\"content\\\":\\\"[rank0]:     loss = closure()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307396\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py\\\\\\\", line 108, in _wrap_closure\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307414\\\",\\\"content\\\":\\\"[rank0]:     closure_result = closure()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307419\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 144, in __call__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307437\\\",\\\"content\\\":\\\"[rank0]:     self._result = self.closure(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307442\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py\\\\\\\", line 116, in decorate_context\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307457\\\",\\\"content\\\":\\\"[rank0]:     return func(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307461\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 129, in closure\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307478\\\",\\\"content\\\":\\\"[rank0]:     step_output = self._step_fn()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307481\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py\\\\\\\", line 317, in _training_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307507\\\",\\\"content\\\":\\\"[rank0]:     training_step_output = call._call_strategy_hook(trainer, \\\\\\\"training_step\\\\\\\", *kwargs.values())\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307519\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 311, in _call_strategy_hook\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307543\\\",\\\"content\\\":\\\"[rank0]:     output = fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307548\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 389, in training_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307566\\\",\\\"content\\\":\\\"[rank0]:     return self._forward_redirection(self.model, self.lightning_module, \\\\\\\"training_step\\\\\\\", *args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307576\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 640, in __call__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307592\\\",\\\"content\\\":\\\"[rank0]:     wrapper_output = wrapper_module(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307598\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1553, in _wrapped_call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307614\\\",\\\"content\\\":\\\"[rank0]:     return self._call_impl(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307625\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1562, in _call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307645\\\",\\\"content\\\":\\\"[rank0]:     return forward_call(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307651\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\\\\\\\", line 1636, in forward\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307670\\\",\\\"content\\\":\\\"[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307677\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py\\\\\\\", line 1454, in _run_ddp_forward\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307698\\\",\\\"content\\\":\\\"[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307705\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1553, in _wrapped_call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307719\\\",\\\"content\\\":\\\"[rank0]:     return self._call_impl(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307724\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py\\\\\\\", line 1562, in _call_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307738\\\",\\\"content\\\":\\\"[rank0]:     return forward_call(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307745\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 633, in wrapped_forward\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307764\\\",\\\"content\\\":\\\"[rank0]:     out = method(*_args, **_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307768\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py\\\\\\\", line 254, in training_step\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307784\\\",\\\"content\\\":\\\"[rank0]:     pred_kspace=output_dict['pred_kspace']\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:56:43.307788\\\",\\\"content\\\":\\\"[rank0]: KeyError: 'pred_kspace'\\\"}\"]},\"wandb-events.jsonl\":{\"offset\":2,\"content\":[\"{\\\"system.gpu.0.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.0.smClock\\\":1980,\\\"system.gpu.0.memoryClock\\\":2619,\\\"system.gpu.1.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.1.smClock\\\":345,\\\"system.gpu.2.memory\\\":0,\\\"system.gpu.2.correctedMemoryErrors\\\":0,\\\"_runtime\\\":30.523670338,\\\"system.gpu.3.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.3.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.2.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.2.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.3.temp\\\":31,\\\"system.gpu.0.powerWatts\\\":112.724,\\\"system.gpu.1.powerWatts\\\":70.972,\\\"system.gpu.1.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.3.powerPercent\\\":10.185142857142857,\\\"_timestamp\\\":1.7550502038130023e+09,\\\"system.gpu.2.smClock\\\":345,\\\"system.gpu.3.correctedMemoryErrors\\\":0,\\\"_wandb\\\":true,\\\"system.gpu.0.memoryAllocated\\\":8.588874311847865,\\\"system.gpu.1.temp\\\":25,\\\"system.gpu.1.powerPercent\\\":10.138857142857143,\\\"system.gpu.1.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.2.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.3.powerWatts\\\":71.296,\\\"system.gpu.0.memory\\\":0,\\\"system.gpu.0.temp\\\":35,\\\"system.gpu.0.powerPercent\\\":16.103428571428573,\\\"system.gpu.1.memory\\\":0,\\\"system.gpu.1.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.1.correctedMemoryErrors\\\":0,\\\"system.gpu.2.gpu\\\":0,\\\"system.gpu.2.powerPercent\\\":10.140428571428572,\\\"system.gpu.0.gpu\\\":0,\\\"system.gpu.2.memoryClock\\\":2619,\\\"system.gpu.1.memoryClock\\\":2619,\\\"system.gpu.2.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.2.powerWatts\\\":70.983,\\\"system.gpu.3.memory\\\":0,\\\"system.gpu.3.memoryClock\\\":2619,\\\"system.gpu.3.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.0.correctedMemoryErrors\\\":0,\\\"system.gpu.0.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.1.gpu\\\":0,\\\"system.gpu.2.temp\\\":26,\\\"system.gpu.3.gpu\\\":0,\\\"system.gpu.3.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.3.smClock\\\":345,\\\"system.gpu.0.memoryAllocatedBytes\\\":7345274880}\",\"{\\\"system.cpu\\\":0.9880928849217431,\\\"_wandb\\\":true,\\\"_timestamp\\\":1.7550502038275049e+09,\\\"_runtime\\\":30.538172891,\\\"system.memory_percent\\\":2.3766931925800296,\\\"system.proc.memory.percent\\\":0.5737801429637138,\\\"system.proc.cpu.threads\\\":16,\\\"system.proc.memory.rssMB\\\":11842.2265625,\\\"system.disk./.usagePercent\\\":1.9874434153723153,\\\"system.network.recv\\\":1.485567265e+09,\\\"system.proc.memory.availableMB\\\":2.00378371875e+06,\\\"system.disk./.usageGB\\\":17.446178436279297,\\\"system.network.sent\\\":1.4530097e+07}\"]},\"wandb-summary.json\":{\"offset\":0,\"content\":[\"{\\\"_wandb\\\":{\\\"runtime\\\":30}}\"]}}}"}
{"time":"2025-08-12T18:56:44.158170237-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/0wpkypoo/file_stream"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/output.log, Name: output.log, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=1679b3dce3e72165f31c6108d5209df1adf42f57055796afaa4020837f4c5c8e07b6063adf05e84d02677927a832d49f2f8716e034dab94b4c7e04e1d6562ac75c08e8d4431c3414228f145021dacfd5efca8bcd7517e2cf165caffdc8712e41b96964a17230b82b7e06ff8f97169138fe74e26e197218d8b6bae26c6e6af233cff3253ee223fac06cd5694527499f50c4d469d61a4b111443054dd4dabe0ddeca9449a361c2d435fe53c9f9d289b10df41c72b1f574d8231b635bf90c14eb088f9c8afcf847f0816778c946562b3e77d271473c09eccabbb1f7a9d5d1df04e4fb4d5150a369386985d877ae9d887151084b4811bf7f8e1dad0524eb9036730c&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/wandb-summary.json, Name: wandb-summary.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=44adb95f5d359cf7157049a02ad36030c003e10c1a9a72b73108096532898cffade0a0520f72a19a09c84bb7fa21b78811a8056ae89e3b6d283b7ec7bbeffe1b561849209c29184217246ab0df003aec428ff59f9f9d1f507dc3cf18c03730c6dcf185fdb2332c0ffc1c4c3d0d838873b5395683166f4f8aae6034051b36dcaa6810292f16b615b92437c9cf028e439093f75e491ca2ca616b3d25b08fa5be860b397f35bd0e2f13ec0fb8d302d8430551f46e0034a7bffd5075bfb71c0733e99ccd46c9dffcfb9ae97d23b6b011424f7f0b13d3c358373584872e41c55122b599eff0668bd85247e8bd443eb8008f7c0bb2904cebe17a55f8736c4c4e3475ee&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/wandb-summary.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=44adb95f5d359cf7157049a02ad36030c003e10c1a9a72b73108096532898cffade0a0520f72a19a09c84bb7fa21b78811a8056ae89e3b6d283b7ec7bbeffe1b561849209c29184217246ab0df003aec428ff59f9f9d1f507dc3cf18c03730c6dcf185fdb2332c0ffc1c4c3d0d838873b5395683166f4f8aae6034051b36dcaa6810292f16b615b92437c9cf028e439093f75e491ca2ca616b3d25b08fa5be860b397f35bd0e2f13ec0fb8d302d8430551f46e0034a7bffd5075bfb71c0733e99ccd46c9dffcfb9ae97d23b6b011424f7f0b13d3c358373584872e41c55122b599eff0668bd85247e8bd443eb8008f7c0bb2904cebe17a55f8736c4c4e3475ee&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/output.log","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=1679b3dce3e72165f31c6108d5209df1adf42f57055796afaa4020837f4c5c8e07b6063adf05e84d02677927a832d49f2f8716e034dab94b4c7e04e1d6562ac75c08e8d4431c3414228f145021dacfd5efca8bcd7517e2cf165caffdc8712e41b96964a17230b82b7e06ff8f97169138fe74e26e197218d8b6bae26c6e6af233cff3253ee223fac06cd5694527499f50c4d469d61a4b111443054dd4dabe0ddeca9449a361c2d435fe53c9f9d289b10df41c72b1f574d8231b635bf90c14eb088f9c8afcf847f0816778c946562b3e77d271473c09eccabbb1f7a9d5d1df04e4fb4d5150a369386985d877ae9d887151084b4811bf7f8e1dad0524eb9036730c&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=1679b3dce3e72165f31c6108d5209df1adf42f57055796afaa4020837f4c5c8e07b6063adf05e84d02677927a832d49f2f8716e034dab94b4c7e04e1d6562ac75c08e8d4431c3414228f145021dacfd5efca8bcd7517e2cf165caffdc8712e41b96964a17230b82b7e06ff8f97169138fe74e26e197218d8b6bae26c6e6af233cff3253ee223fac06cd5694527499f50c4d469d61a4b111443054dd4dabe0ddeca9449a361c2d435fe53c9f9d289b10df41c72b1f574d8231b635bf90c14eb088f9c8afcf847f0816778c946562b3e77d271473c09eccabbb1f7a9d5d1df04e4fb4d5150a369386985d877ae9d887151084b4811bf7f8e1dad0524eb9036730c&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=44adb95f5d359cf7157049a02ad36030c003e10c1a9a72b73108096532898cffade0a0520f72a19a09c84bb7fa21b78811a8056ae89e3b6d283b7ec7bbeffe1b561849209c29184217246ab0df003aec428ff59f9f9d1f507dc3cf18c03730c6dcf185fdb2332c0ffc1c4c3d0d838873b5395683166f4f8aae6034051b36dcaa6810292f16b615b92437c9cf028e439093f75e491ca2ca616b3d25b08fa5be860b397f35bd0e2f13ec0fb8d302d8430551f46e0034a7bffd5075bfb71c0733e99ccd46c9dffcfb9ae97d23b6b011424f7f0b13d3c358373584872e41c55122b599eff0668bd85247e8bd443eb8008f7c0bb2904cebe17a55f8736c4c4e3475ee&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(3))","buffer":0}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.225090284-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.225095655-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(4))","buffer":0}
{"time":"2025-08-12T18:56:44.225098209-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.225100385-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.225102701-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.275265542-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["config.yaml"]}
{"time":"2025-08-12T18:56:44.275338413-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:56:44.279140226-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/config.yaml, Name: config.yaml, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=c2714919b7fdfb90a9fd1519daffc753faa97ec4e1403651810991232d0cc84b884aa7af9005f264e200ec6eb0deecdb4eb681ab6e8055357d598d425d92ccd51aafe57d837d771ae90eedf07fd7f1bf54436c15db276ecef2b9a89fab7d8ef25b0d43e63f4ef8e295c6002c5a39e8bfeac22ca38db2842e18e6cd4f749938557334e0a7b943a6caa3fddbad893607b3fb0efaa69d40afb1cc00da4eb68c929b727cfb4468d40757173962f73d792b4dc51891ba921a760ceda29ec554c058a7479279cab33647f9c85f9f7f7e80967ba420e74b131f340ffcc32a0a662a48adbc6dc79aa972547a6352177a85e2a9d0af1c70e1925b4ebc168d72fce0f0d4d6&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_185613-0wpkypoo/files/config.yaml","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=c2714919b7fdfb90a9fd1519daffc753faa97ec4e1403651810991232d0cc84b884aa7af9005f264e200ec6eb0deecdb4eb681ab6e8055357d598d425d92ccd51aafe57d837d771ae90eedf07fd7f1bf54436c15db276ecef2b9a89fab7d8ef25b0d43e63f4ef8e295c6002c5a39e8bfeac22ca38db2842e18e6cd4f749938557334e0a7b943a6caa3fddbad893607b3fb0efaa69d40afb1cc00da4eb68c929b727cfb4468d40757173962f73d792b4dc51891ba921a760ceda29ec554c058a7479279cab33647f9c85f9f7f7e80967ba420e74b131f340ffcc32a0a662a48adbc6dc79aa972547a6352177a85e2a9d0af1c70e1925b4ebc168d72fce0f0d4d6&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:44.********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/0wpkypoo/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T015644Z&X-Goog-Expires=86399&X-Goog-Signature=c2714919b7fdfb90a9fd1519daffc753faa97ec4e1403651810991232d0cc84b884aa7af9005f264e200ec6eb0deecdb4eb681ab6e8055357d598d425d92ccd51aafe57d837d771ae90eedf07fd7f1bf54436c15db276ecef2b9a89fab7d8ef25b0d43e63f4ef8e295c6002c5a39e8bfeac22ca38db2842e18e6cd4f749938557334e0a7b943a6caa3fddbad893607b3fb0efaa69d40afb1cc00da4eb68c929b727cfb4468d40757173962f73d792b4dc51891ba921a760ceda29ec554c058a7479279cab33647f9c85f9f7f7e80967ba420e74b131f340ffcc32a0a662a48adbc6dc79aa972547a6352177a85e2a9d0af1c70e1925b4ebc168d72fce0f0d4d6&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-summary.json"}}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"output.log"}}
{"time":"2025-08-12T18:56:44.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"config.yaml"}}
{"time":"2025-08-12T18:56:44.558131385-07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-12T18:56:44.558138073-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(5))","buffer":0}
{"time":"2025-08-12T18:56:44.558143775-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.558148041-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.5581511-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.558154314-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"ExitCode":1}}
{"time":"2025-08-12T18:56:44.558186626-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"uploaded\":[\"wandb-summary.json\",\"output.log\",\"config.yaml\"],\"complete\":true,\"exitcode\":1}"}
{"time":"2025-08-12T18:56:44.558202052-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/0wpkypoo/file_stream"}
{"time":"2025-08-12T18:56:44.65443515-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":1,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:56:44.654462629-07:00","level":"DEBUG","msg":"filestream: closed"}
{"time":"2025-08-12T18:56:44.654469051-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(6))","buffer":0}
{"time":"2025-08-12T18:56:44.654476808-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.654481567-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.654484684-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.65449059-07:00","level":"INFO","msg":"handler: closed","stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.654493228-07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.654501874-07:00","level":"INFO","msg":"sender: closed","stream_id":"0wpkypoo"}
{"time":"2025-08-12T18:56:44.65647853-07:00","level":"INFO","msg":"stream: closed","id":"0wpkypoo"}
