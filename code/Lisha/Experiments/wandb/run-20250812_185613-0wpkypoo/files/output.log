Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                 | Params | Mode
------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum | 0      | train
1 | SSIM             | DistributedMetricSum | 0      | train
2 | PSNR             | DistributedMetricSum | 0      | train
3 | ValLoss          | DistributedMetricSum | 0      | train
4 | TotExamples      | DistributedMetricSum | 0      | train
5 | TotSliceExamples | DistributedMetricSum | 0      | train
6 | promptmr         | PromptMR             | 125 M  | train
7 | normalized_l1    | normalized_l1_loss   | 0      | train
8 | loss             | SSIMLoss             | 0      | train
------------------------------------------------------------------
111 <USER>     <GROUP> params
13.7 M    Non-trainable params
125 M     Total params
502.227   Total estimated model params size (MB)
SLURM auto-requeueing enabled. Setting signal handlers.
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]mask: torch.Size([1, 516, 161])
output shape: torch.Size([1, 516, 161])
Sanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00<00:00,  1.56it/s]mask: torch.Size([1, 516, 161])
output shape: torch.Size([1, 516, 161])
Epoch 0:   0%|          | 0/55879 [00:00<?, ?it/s] mask shape: torch.Size([1, 70, 224, 87, 1])
data shape: torch.Size([1, 70, 224, 87, 2])
Traceback (most recent call last):
  File "main.py", line 244, in <module>
    run_cli()
  File "main.py", line 236, in run_cli
    cli = CustomLightningCLI(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 394, in __init__
    self._run_subcommand(self.subcommand)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 701, in _run_subcommand
    fn(**fn_kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 543, in fit
    call._call_and_handle_interrupt(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 43, in _call_and_handle_interrupt
    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
    return function(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 579, in _fit_impl
    self._run(model, ckpt_path=ckpt_path)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 986, in _run
    results = self._run_stage()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 1030, in _run_stage
    self.fit_loop.run()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py", line 205, in run
    self.advance()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py", line 363, in advance
    self.epoch_loop.run(self._data_fetcher)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py", line 140, in run
    self.advance(data_fetcher)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py", line 250, in advance
    batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 190, in run
    self._optimizer_step(batch_idx, closure)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 268, in _optimizer_step
    call._call_lightning_module_hook(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 159, in _call_lightning_module_hook
    output = fn(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py", line 1308, in optimizer_step
    optimizer.step(closure=optimizer_closure)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py", line 153, in step
    step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py", line 270, in optimizer_step
    optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 238, in optimizer_step
    return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py", line 122, in optimizer_step
    return optimizer.step(closure=closure, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py", line 484, in wrapper
    out = func(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py", line 89, in _use_grad
    ret = func(self, *args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py", line 204, in step
    loss = closure()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py", line 108, in _wrap_closure
    closure_result = closure()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 144, in __call__
    self._result = self.closure(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
    return func(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 129, in closure
    step_output = self._step_fn()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 317, in _training_step
    training_step_output = call._call_strategy_hook(trainer, "training_step", *kwargs.values())
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 311, in _call_strategy_hook
    output = fn(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 389, in training_step
    return self._forward_redirection(self.model, self.lightning_module, "training_step", *args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 640, in __call__
    wrapper_output = wrapper_module(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py", line 1636, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py", line 1454, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 633, in wrapped_forward
    out = method(*_args, **_kwargs)
  File "/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py", line 254, in training_step
    pred_kspace=output_dict['pred_kspace']
KeyError: 'pred_kspace'
[rank0]: Traceback (most recent call last):
[rank0]:   File "main.py", line 244, in <module>
[rank0]:     run_cli()
[rank0]:   File "main.py", line 236, in run_cli
[rank0]:     cli = CustomLightningCLI(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 394, in __init__
[rank0]:     self._run_subcommand(self.subcommand)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 701, in _run_subcommand
[rank0]:     fn(**fn_kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 543, in fit
[rank0]:     call._call_and_handle_interrupt(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 43, in _call_and_handle_interrupt
[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
[rank0]:     return function(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 579, in _fit_impl
[rank0]:     self._run(model, ckpt_path=ckpt_path)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 986, in _run
[rank0]:     results = self._run_stage()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 1030, in _run_stage
[rank0]:     self.fit_loop.run()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py", line 205, in run
[rank0]:     self.advance()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/fit_loop.py", line 363, in advance
[rank0]:     self.epoch_loop.run(self._data_fetcher)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py", line 140, in run
[rank0]:     self.advance(data_fetcher)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/training_epoch_loop.py", line 250, in advance
[rank0]:     batch_output = self.automatic_optimization.run(trainer.optimizers[0], batch_idx, kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 190, in run
[rank0]:     self._optimizer_step(batch_idx, closure)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 268, in _optimizer_step
[rank0]:     call._call_lightning_module_hook(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 159, in _call_lightning_module_hook
[rank0]:     output = fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/module.py", line 1308, in optimizer_step
[rank0]:     optimizer.step(closure=optimizer_closure)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/core/optimizer.py", line 153, in step
[rank0]:     step_output = self._strategy.optimizer_step(self._optimizer, closure, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/ddp.py", line 270, in optimizer_step
[rank0]:     optimizer_output = super().optimizer_step(optimizer, closure, model, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 238, in optimizer_step
[rank0]:     return self.precision_plugin.optimizer_step(optimizer, model=model, closure=closure, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py", line 122, in optimizer_step
[rank0]:     return optimizer.step(closure=closure, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py", line 484, in wrapper
[rank0]:     out = func(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py", line 89, in _use_grad
[rank0]:     ret = func(self, *args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/adamw.py", line 204, in step
[rank0]:     loss = closure()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/plugins/precision/precision.py", line 108, in _wrap_closure
[rank0]:     closure_result = closure()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 144, in __call__
[rank0]:     self._result = self.closure(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 129, in closure
[rank0]:     step_output = self._step_fn()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/loops/optimization/automatic.py", line 317, in _training_step
[rank0]:     training_step_output = call._call_strategy_hook(trainer, "training_step", *kwargs.values())
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 311, in _call_strategy_hook
[rank0]:     output = fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 389, in training_step
[rank0]:     return self._forward_redirection(self.model, self.lightning_module, "training_step", *args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 640, in __call__
[rank0]:     wrapper_output = wrapper_module(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py", line 1636, in forward
[rank0]:     else self._run_ddp_forward(*inputs, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/parallel/distributed.py", line 1454, in _run_ddp_forward
[rank0]:     return self.module(*inputs, **kwargs)  # type: ignore[index]
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1553, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/nn/modules/module.py", line 1562, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 633, in wrapped_forward
[rank0]:     out = method(*_args, **_kwargs)
[rank0]:   File "/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3/pl_modules/promptmr_module.py", line 254, in training_step
[rank0]:     pred_kspace=output_dict['pred_kspace']
[rank0]: KeyError: 'pred_kspace'
