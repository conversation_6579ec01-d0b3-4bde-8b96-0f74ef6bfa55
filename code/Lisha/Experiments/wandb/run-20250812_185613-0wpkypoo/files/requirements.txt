async-timeout==5.0.1
scipy==1.10.1
matplotlib==3.7.5
annotated-types==0.7.0
pip==24.2
filelock==3.16.1
networkx==3.1
einops==0.8.1
nvidia-cufft-cu12==*********
pytorch-lightning==2.0.0
nvidia-cusolver-cu12==**********
Markdown==3.7
oauthlib==3.2.2
pyasn1==0.6.1
eval_type_backport==0.2.2
rsa==4.9
platformdirs==4.3.6
google-auth-oauthlib==1.0.0
lazy_loader==0.4
nvidia-nccl-cu12==2.20.5
torchvision==0.13.0
attrs==25.3.0
urllib3==2.2.3
nvidia-cuda-cupti-cu12==12.1.105
lightning-utilities==0.11.9
pydantic==2.10.6
nvidia-curand-cu12==**********
pydantic_core==2.27.2
PyWavelets==1.4.1
packaging==24.2
python-dateutil==2.9.0.post0
certifi==2024.8.30
PySocks==1.7.1
nvidia-nvjitlink-cu12==12.8.93
importlib_resources==6.4.5
cachetools==5.5.2
kiwisolver==1.4.7
tifffile==2023.7.10
tensorboard-data-server==0.7.2
zipp==3.20.2
torchmetrics==1.5.2
pyparsing==3.1.4
Jinja2==3.1.6
nvidia-cudnn-cu12==********
protobuf==5.29.4
jsonargparse==4.38.0
Brotli==1.0.9
idna==3.7
typing_extensions==4.13.2
mkl-random==1.2.4
sympy==1.13.3
contourpy==1.1.1
mkl-fft==1.3.8
docker-pycreds==0.4.0
setuptools==75.1.0
aiosignal==1.3.1
numpy==1.24.3
pyasn1_modules==0.4.2
pillow==10.4.0
absl-py==2.2.2
mkl-service==2.4.0
multidict==6.1.0
six==1.17.0
lightning==2.3.3
fsspec==2025.3.0
mpmath==1.3.0
gitdb==4.0.12
wandb==0.19.9
fonttools==4.57.0
psutil==7.0.0
nvidia-cuda-nvrtc-cu12==12.1.105
frozenlist==1.5.0
aiohappyeyeballs==2.4.4
PyYAML==6.0.2
requests==2.32.3
yarl==1.15.2
typeshed_client==2.7.0
smmap==5.0.2
h5py==3.11.0
tqdm==4.67.1
torch==2.4.1
nvidia-cuda-runtime-cu12==12.1.105
triton==3.0.0
charset-normalizer==3.3.2
docstring_parser==0.16
Werkzeug==3.0.6
MarkupSafe==2.1.5
wheel==0.44.0
nvidia-cusparse-cu12==**********
torchaudio==0.12.0
requests-oauthlib==2.0.0
tensorboard==2.14.0
grpcio==1.70.0
scikit-image==0.21.0
propcache==0.2.0
nvidia-nvtx-cu12==12.1.105
google-auth==2.38.0
sentry-sdk==2.25.1
nvidia-cublas-cu12==********
GitPython==3.1.44
setproctitle==1.3.5
cycler==0.12.1
importlib_metadata==8.5.0
aiohttp==3.10.11
imageio==2.35.1
click==8.1.8
jaraco.context==5.3.0
tomli==2.0.1
jaraco.text==3.12.1
wheel==0.43.0
typing_extensions==4.12.2
importlib_resources==6.4.0
packaging==24.1
platformdirs==4.2.2
autocommand==2.2.2
jaraco.functools==4.0.1
inflect==7.3.1
typeguard==4.3.0
backports.tarfile==1.2.0
more-itertools==10.3.0
zipp==3.19.2
jaraco.collections==5.1.0
importlib_metadata==8.0.0
