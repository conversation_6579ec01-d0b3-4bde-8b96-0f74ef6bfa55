_instantiator:
    value: lightning.pytorch.cli.instantiate_module
_wandb:
    value:
        cli_version: 0.19.9
        m:
            - "1": trainer/global_step
              "6":
                - 3
              "7": []
        python_version: 3.8.20
        t:
            "1":
                - 1
                - 55
                - 106
            "2":
                - 1
                - 55
                - 106
            "3":
                - 7
                - 13
                - 15
                - 18
                - 23
                - 55
                - 66
            "4": 3.8.20
            "5": 0.19.9
            "8":
                - 5
            "12": 0.19.9
            "13": linux-x86_64
class_path:
    value: pl_modules.PromptMrModule
init_args:
    value:
        adaptive_input: true
        compute_sens_per_coil: false
        feature_dim:
            - 72
            - 96
            - 120
        learnable_prompt: false
        len_prompt:
            - 5
            - 5
            - 5
        lr: 0.0002
        lr_gamma: 0.1
        lr_step_size: 11
        model_version: promptmr_v2
        n_bottleneck_cab: 3
        n_buffer: 4
        n_dec_cab:
            - 2
            - 2
            - 3
        n_enc_cab:
            - 2
            - 3
            - 3
        n_feat0: 48
        n_history: 15
        n_skip_cab:
            - 1
            - 1
            - 1
        no_use_ca: false
        num_adj_slices: 7
        num_cascades: 16
        num_log_images: 16
        pretrain: true
        pretrain_weights_path: /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt
        prompt_dim:
            - 24
            - 48
            - 72
        prompt_size:
            - 64
            - 32
            - 16
        sens_feature_dim:
            - 36
            - 48
            - 60
        sens_n_feat0: 24
        sens_prompt_dim:
            - 12
            - 24
            - 36
        use_checkpoint: false
        use_sens_adj: true
        weight_decay: 0.01
