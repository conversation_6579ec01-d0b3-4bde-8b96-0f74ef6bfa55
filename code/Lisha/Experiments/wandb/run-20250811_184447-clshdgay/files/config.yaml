_instantiator:
    value: lightning.pytorch.cli.instantiate_module
_wandb:
    value:
        cli_version: 0.19.9
        m:
            - "1": val_images_idx_3334.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": trainer/global_step
              "6":
                - 3
              "7": []
            - "1": val_images_idx_7386.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/psnr
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": epoch
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3334.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7386.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/nmse
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3334.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7386.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": validation_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": train_loss
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7386.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7386._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_metrics/ssim
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3334._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3334.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7386.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_2835.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5628.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1373.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3334.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_4769.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5574.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_453.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_1400.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3334.format
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6739.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7386.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_239.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688.height
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_7688.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737.captions
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_737.width
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_3467.filenames
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_6255._type
              "5": 2
              "6":
                - 1
                - 3
              "7": []
            - "1": val_images_idx_5351.count
              "5": 2
              "6":
                - 1
                - 3
              "7": []
        python_version: 3.8.20
        t:
            "1":
                - 1
                - 55
                - 106
            "2":
                - 1
                - 41
                - 55
                - 106
            "3":
                - 7
                - 13
                - 15
                - 18
                - 23
                - 55
                - 66
            "4": 3.8.20
            "5": 0.19.9
            "8":
                - 5
            "12": 0.19.9
            "13": linux-x86_64
class_path:
    value: pl_modules.PromptMrModule
init_args:
    value:
        adaptive_input: true
        compute_sens_per_coil: false
        feature_dim:
            - 72
            - 96
            - 120
        learnable_prompt: false
        len_prompt:
            - 5
            - 5
            - 5
        lr: 0.0002
        lr_gamma: 0.1
        lr_step_size: 11
        model_version: promptmr_v2
        n_bottleneck_cab: 3
        n_buffer: 4
        n_dec_cab:
            - 2
            - 2
            - 3
        n_enc_cab:
            - 2
            - 3
            - 3
        n_feat0: 48
        n_history: 11
        n_skip_cab:
            - 1
            - 1
            - 1
        no_use_ca: false
        num_adj_slices: 5
        num_cascades: 12
        num_log_images: 16
        pretrain: false
        prompt_dim:
            - 24
            - 48
            - 72
        prompt_size:
            - 64
            - 32
            - 16
        sens_feature_dim:
            - 36
            - 48
            - 60
        sens_n_feat0: 24
        sens_prompt_dim:
            - 12
            - 24
            - 36
        use_checkpoint: false
        use_sens_adj: true
        weight_decay: 0.01
