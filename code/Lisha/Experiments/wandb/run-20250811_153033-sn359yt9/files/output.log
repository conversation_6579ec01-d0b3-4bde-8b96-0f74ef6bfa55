Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/sn359yt9/config.yaml
Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/wkzc6pb5/checkpoints/best-epochepoch=03-valvalidation_loss=0.0266.ckpt
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/callbacks/model_checkpoint.py:360: The dirpath has changed from '/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/wkzc6pb5/checkpoints' to '/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/sn359yt9/checkpoints', therefore `best_model_score`, `kth_best_model_path`, `kth_value`, `last_model_path` and `best_k_models` won't be reloaded. Only `best_model_path` will be reloaded.
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                 | Params | Mode
------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum | 0      | train
1 | SSIM             | DistributedMetricSum | 0      | train
2 | PSNR             | DistributedMetricSum | 0      | train
3 | ValLoss          | DistributedMetricSum | 0      | train
4 | TotExamples      | DistributedMetricSum | 0      | train
5 | TotSliceExamples | DistributedMetricSum | 0      | train
6 | promptmr         | PromptMR             | 92.9 M | train
7 | loss             | SSIMLoss             | 0      | train
------------------------------------------------------------------
82.5 M    Trainable params
10.4 M    Non-trainable params
92.9 M    Total params
371.436   Total estimated model params size (MB)
Restored all states from the checkpoint at /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/wkzc6pb5/checkpoints/best-epochepoch=03-valvalidation_loss=0.0266.ckpt
SLURM auto-requeueing enabled. Setting signal handlers.
Epoch 6:  69%|██████▉   | 105276/152656 [10:56:21<4:55:23,  2.67it/s, v_num=9yt9, train_loss=0.0105, validation_loss=0.0241]  
                                                                            
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory
  warn(f"Failed to load image Python extension: {e}")
Epoch 4, global step 763280: 'validation_loss' reached 0.02534 (best 0.02534), saving model to '/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/sn359yt9/checkpoints/best-epochepoch=04-valvalidation_loss=0.0253.ckpt' as top 1
Epoch 5, global step 915936: 'validation_loss' reached 0.02414 (best 0.02414), saving model to '/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/sn359yt9/checkpoints/best-epochepoch=05-valvalidation_loss=0.0241.ckpt' as top 1
