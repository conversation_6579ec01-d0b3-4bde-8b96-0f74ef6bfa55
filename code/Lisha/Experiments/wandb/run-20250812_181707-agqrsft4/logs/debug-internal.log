{"time":"2025-08-12T18:17:07.961818802-07:00","level":"INFO","msg":"stream: starting","core version":"0.19.9","symlink path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/logs/debug-core.log"}
{"time":"2025-08-12T18:17:07.961886295-07:00","level":"DEBUG","msg":"monitor: sampling interval: 15s"}
{"time":"2025-08-12T18:17:08.076638287-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.158497343-07:00","level":"INFO","msg":"created new stream","id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.158521752-07:00","level":"INFO","msg":"stream: started","id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.158536425-07:00","level":"INFO","msg":"writer: Do: started","stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.158554705-07:00","level":"INFO","msg":"handler: started","stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.15858297-07:00","level":"INFO","msg":"sender: started","stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.159034029-07:00","level":"DEBUG","msg":"handling record","record":{"Header":{}}}
{"time":"2025-08-12T18:17:08.15905296-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Header); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.159123071-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Header":{}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.159435397-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"agqrsft4","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:17:08.159447971-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(mailbox_slot:\"8b7ozqpqj4uj\" connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.159457414-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"agqrsft4","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"8b7ozqpqj4uj","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.160207316-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.160241428-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"agqrsft4","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"8b7ozqpqj4uj","always_send":true,"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.160286462-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":1,"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.160293303-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":2,"RecordType":{"Run":{"run_id":"agqrsft4","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"8b7ozqpqj4uj","always_send":true,"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.160436887-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.344172471-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"RunResult":{"run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","storage_id":"UnVuOnYxOmFncXJzZnQ0OmNtcjIwMjVfdGFzazM6bGlzaGEtemVuZy1jZWRhcnMtc2luYWk=","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}},"control":{"mailbox_slot":"8b7ozqpqj4uj","always_send":true,"connection_id":"127.0.0.1:44126"}}}
{"time":"2025-08-12T18:17:08.345833886-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}}}
{"time":"2025-08-12T18:17:08.345855798-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_RunStart); Control(local:true mailbox_slot:\"zdeazzndzn5p\" connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.345864282-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"zdeazzndzn5p","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.345964062-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"zdeazzndzn5p","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.346049229-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"zdeazzndzn5p","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.346072575-07:00","level":"DEBUG","msg":"filestream: start","path":""}
{"time":"2025-08-12T18:17:08.346096-07:00","level":"DEBUG","msg":"filestream: open","path":"files/lisha-zeng-cedars-sinai/cmr2025_task3/agqrsft4/file_stream"}
{"time":"2025-08-12T18:17:08.349105839-07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-12T18:17:08.349123534-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":null}},"control":{"local":true,"mailbox_slot":"zdeazzndzn5p","connection_id":"127.0.0.1:44126"}}}
{"time":"2025-08-12T18:17:08.349178584-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.349669258-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":3,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.381179123-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_Metadata); Control(<nil>)","buffer":0}
{"time":"2025-08-12T18:17:08.381201879-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"Metadata":{"cpu_count":96,"cpu_count_logical":96,"gpu_type":"NVIDIA H100 80GB HBM3","gpu_count":4,"disk":{"/":{"total":942552190976,"used":18732400640}},"memory":{"total":2164152020992},"cpu":{"count":96,"count_logical":96},"gpu_nvidia":[{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"}],"slurm":{"cluster_name":"slurm-compbio","conf":"/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf","cpu_bind":"quiet,mask_cpu:0x00000000000F000000000017","cpu_bind_list":"0x00000000000F000000000017","cpu_bind_type":"mask_cpu:","cpu_bind_verbose":"quiet","cpus_on_node":"8","cpus_per_task":"8","distribution":"cyclic","gpus_on_node":"4","gtids":"0","job_account":"user","job_cpus_per_node":"8","job_end_time":"**********","job_gid":"23023","job_gpus":"0,1,2,3","job_id":"2155076","job_name":"cmr_baselineXL_task3","job_nodelist":"esplhpc-cp088","job_num_nodes":"1","job_partition":"gpu","job_qos":"normal","job_start_time":"**********","job_uid":"1235884","job_user":"zengl2","jobid":"2155076","launch_node_ipaddr":"************","localid":"0","mem_per_node":"131072","nnodes":"1","nodeid":"0","nodelist":"esplhpc-cp088","nprocs":"1","ntasks":"1","prio_process":"0","procid":"0","srun_comm_host":"************","srun_comm_port":"40737","step_gpus":"0,1,2,3","step_id":"0","step_launcher_port":"40737","step_nodelist":"esplhpc-cp088","step_num_nodes":"1","step_num_tasks":"1","step_tasks_per_node":"1","stepid":"0","submit_dir":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3","submit_host":"esplhpccompbio-lv02","task_pid":"1712266","tasks_per_node":"1","topology_addr":"esplhpc-cp088","topology_addr_pattern":"node","tres_per_task":"cpu:8","umask":"0022"},"cuda_version":"12.4"}}}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.38502921-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.385045016-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":4,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.400422612-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["wandb-metadata.json"]}
{"time":"2025-08-12T18:17:08.400531086-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.43511047-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}}}
{"time":"2025-08-12T18:17:08.435150205-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_PythonPackages); Control(local:true connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.435159226-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}},"control":{"local":true,"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.437349217-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.437382583-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":5,"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/wandb-metadata.json, Name: wandb-metadata.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011708Z&X-Goog-Expires=86399&X-Goog-Signature=748ab6b820d95f2a35a2aeaa33e7d538b086814c081f50cc413448cf9edb29b12a93a4071db17e297bd42e151b2a326c7f6bbcc69dfe49ecbc2eaae9dd994327b6a1dc1c32057a2d04b7433ea145a3b1192fa15cb08a875297569e3e67dc93f6b242a3946b6bb8685dfefd812d55c194236709edae1b3f925b53a604b6c3a5fb65af14703a8b0390f3582bc330e0ed68768b9e7d76eb8a2c0420ee8293de0d379bf9810dce3b293c1a3a00ed2634d989aa664b803c9113a01c93635bf95138351cf03924d1dd11f5c5c1b5fc6140da2362e54ae45c7270201623e7fa320cfc56a6a74468cbe8337a0244c35e6235c7dd30f62adb4c267c50322e5ae6fb398802&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/wandb-metadata.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011708Z&X-Goog-Expires=86399&X-Goog-Signature=748ab6b820d95f2a35a2aeaa33e7d538b086814c081f50cc413448cf9edb29b12a93a4071db17e297bd42e151b2a326c7f6bbcc69dfe49ecbc2eaae9dd994327b6a1dc1c32057a2d04b7433ea145a3b1192fa15cb08a875297569e3e67dc93f6b242a3946b6bb8685dfefd812d55c194236709edae1b3f925b53a604b6c3a5fb65af14703a8b0390f3582bc330e0ed68768b9e7d76eb8a2c0420ee8293de0d379bf9810dce3b293c1a3a00ed2634d989aa664b803c9113a01c93635bf95138351cf03924d1dd11f5c5c1b5fc6140da2362e54ae45c7270201623e7fa320cfc56a6a74468cbe8337a0244c35e6235c7dd30f62adb4c267c50322e5ae6fb398802&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011708Z&X-Goog-Expires=86399&X-Goog-Signature=748ab6b820d95f2a35a2aeaa33e7d538b086814c081f50cc413448cf9edb29b12a93a4071db17e297bd42e151b2a326c7f6bbcc69dfe49ecbc2eaae9dd994327b6a1dc1c32057a2d04b7433ea145a3b1192fa15cb08a875297569e3e67dc93f6b242a3946b6bb8685dfefd812d55c194236709edae1b3f925b53a604b6c3a5fb65af14703a8b0390f3582bc330e0ed68768b9e7d76eb8a2c0420ee8293de0d379bf9810dce3b293c1a3a00ed2634d989aa664b803c9113a01c93635bf95138351cf03924d1dd11f5c5c1b5fc6140da2362e54ae45c7270201623e7fa320cfc56a6a74468cbe8337a0244c35e6235c7dd30f62adb4c267c50322e5ae6fb398802&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"j609usw6iek4\" connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.475827659-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"NetworkStatus":{}}}}}
{"time":"2025-08-12T18:17:08.475832806-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_NetworkStatus); Control(local:true mailbox_slot:\"y2n546cceg3c\" connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.475860269-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:17:08.475873859-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:08.475876399-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"j609usw6iek4","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475893261-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"y2n546cceg3c","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475897686-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475913405-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"j609usw6iek4","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475916716-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:17:08.475922999-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"qp3zo693sw1t\" connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.475917217-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"y2n546cceg3c","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475927602-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475936-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:17:08.475943385-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.475951259-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"j609usw6iek4","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.475993462-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.476000006-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}}}
{"time":"2025-08-12T18:17:08.476006116-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:08.476015944-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:17:08.47602321-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:08.476031252-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}}}
{"time":"2025-08-12T18:17:08.476036391-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:44126\")","buffer":3}
{"time":"2025-08-12T18:17:08.476048513-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:17:08.47604931-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"qp3zo693sw1t","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476054987-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:44126\")","buffer":4}
{"time":"2025-08-12T18:17:08.476058251-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476066556-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476070711-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:17:08.476080508-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:08.476076445-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476083887-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476088588-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476094443-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476105743-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476113051-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476116154-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476086921-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"qp3zo693sw1t","connection_id":"127.0.0.1:44126"}}}
{"time":"2025-08-12T18:17:08.476121491-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.476125336-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.47613043-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.489289092-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["requirements.txt"]}
{"time":"2025-08-12T18:17:08.489375625-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/requirements.txt, Name: requirements.txt, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011708Z&X-Goog-Expires=86399&X-Goog-Signature=a15eed96ceaed5d2678b91034e94f203293e02a22adaf67d0247b2c7a80539b2ca229b8833164f95c39145cde10e7b9ddfc7577612233090fae90c34792189282bb4034af6acb798db594cb34f0119c9c99cb6793aecf080468759d834c6fb12b300ee24ac2c7e31f3381f3b99a0c210c7dad8456c2e043b753ef348ddf1253cccb758826791018cf958193aff37d3a1b8eba7dd235cd26b062a94c9e0fb121ce4b7283f18513dc25704d8ada6ef570b9a47b2876a2dacad10a2a0ac931df267e93245265fbcfec3408c08f4c846e82257b536a82bf093432178be439a618f49570cf327a6f0cf38639536931b754a2c106eea2cb1415b447bc8baaf86fe0c00&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/requirements.txt","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011708Z&X-Goog-Expires=86399&X-Goog-Signature=a15eed96ceaed5d2678b91034e94f203293e02a22adaf67d0247b2c7a80539b2ca229b8833164f95c39145cde10e7b9ddfc7577612233090fae90c34792189282bb4034af6acb798db594cb34f0119c9c99cb6793aecf080468759d834c6fb12b300ee24ac2c7e31f3381f3b99a0c210c7dad8456c2e043b753ef348ddf1253cccb758826791018cf958193aff37d3a1b8eba7dd235cd26b062a94c9e0fb121ce4b7283f18513dc25704d8ada6ef570b9a47b2876a2dacad10a2a0ac931df267e93245265fbcfec3408c08f4c846e82257b536a82bf093432178be439a618f49570cf327a6f0cf38639536931b754a2c106eea2cb1415b447bc8baaf86fe0c00&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011708Z&X-Goog-Expires=86399&X-Goog-Signature=a15eed96ceaed5d2678b91034e94f203293e02a22adaf67d0247b2c7a80539b2ca229b8833164f95c39145cde10e7b9ddfc7577612233090fae90c34792189282bb4034af6acb798db594cb34f0119c9c99cb6793aecf080468759d834c6fb12b300ee24ac2c7e31f3381f3b99a0c210c7dad8456c2e043b753ef348ddf1253cccb758826791018cf958193aff37d3a1b8eba7dd235cd26b062a94c9e0fb121ce4b7283f18513dc25704d8ada6ef570b9a47b2876a2dacad10a2a0ac931df267e93245265fbcfec3408c08f4c846e82257b536a82bf093432178be439a618f49570cf327a6f0cf38639536931b754a2c106eea2cb1415b447bc8baaf86fe0c00&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:08.*********-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"y2n546cceg3c","connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.592699103-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":6,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.59270318-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"j609usw6iek4","connection_id":"127.0.0.1:44126"}}}
{"time":"2025-08-12T18:17:08.592752844-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:17:08.592875961-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.705025781-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-metadata.json"}}
{"time":"2025-08-12T18:17:08.705127016-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"uploaded\":[\"wandb-metadata.json\"]}"}
{"time":"2025-08-12T18:17:08.705151229-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/agqrsft4/file_stream"}
{"time":"2025-08-12T18:17:08.729122134-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"requirements.txt"}}
{"time":"2025-08-12T18:17:08.787068402-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786898000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/agqrsft4/config.yaml"}}}
{"time":"2025-08-12T18:17:08.787091156-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.787100401-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786898000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/agqrsft4/config.yaml"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.787107162-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786898000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/agqrsft4/config.yaml"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.792642922-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":7,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.792703108-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":8,"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.792716078-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":9,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.79279167-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":10,"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.792798295-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":11,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.792825595-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":12,"RecordType":{"Run":{"run_id":"agqrsft4","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047827,"nanos":955918000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.792896646-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:08.798649768-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:17:08.827710459-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786984000},"line":"\n"}}}
{"time":"2025-08-12T18:17:08.827728527-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.827740371-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":788180000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}}}
{"time":"2025-08-12T18:17:08.827745364-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:08.827760195-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":790178000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}}}
{"time":"2025-08-12T18:17:08.827765152-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:08.827773561-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786984000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.827779721-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":788180000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.827783293-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":790178000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.827788144-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786984000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.827792261-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":788180000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.827794951-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":790178000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.90624924-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":13,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786898000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/agqrsft4/config.yaml"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.906307524-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":14,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":786984000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.906312102-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":15,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":788180000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.906367373-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":16,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":790178000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:08.910498631-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:08.920118252-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:09.720781328-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":720398000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}}}
{"time":"2025-08-12T18:17:09.720834682-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:09.720852501-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":720398000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.720868884-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":720398000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.720891888-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":17,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":720398000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.723951005-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:09.819710482-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819442000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}}}
{"time":"2025-08-12T18:17:09.819739644-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:09.81975132-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819442000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.819762347-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819442000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.819770779-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":18,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819442000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.819815346-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819697000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}}}
{"time":"2025-08-12T18:17:09.819838424-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:09.819846056-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819697000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.819856127-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819697000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.819952645-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":19,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":819697000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:09.823161508-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:09.832509574-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:10.048450957-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":48195000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}}}
{"time":"2025-08-12T18:17:10.048469095-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.048475708-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":48195000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.048492009-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":48195000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.048498943-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":20,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":48195000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.049192062-07:00","level":"DEBUG","msg":"handling record","record":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}}}
{"time":"2025-08-12T18:17:10.049240946-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Config); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.049253497-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.049303423-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.049327725-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":21,"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.05155782-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:10.165091223-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164854000},"line":"Traceback (most recent call last):\n"}}}
{"time":"2025-08-12T18:17:10.165110168-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165124261-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164920000}}}}
{"time":"2025-08-12T18:17:10.165128649-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165137846-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164968000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}}}
{"time":"2025-08-12T18:17:10.165142601-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:10.165155573-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164985000}}}}
{"time":"2025-08-12T18:17:10.165159395-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:10.165166806-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165000000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}}}
{"time":"2025-08-12T18:17:10.165170825-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":3}
{"time":"2025-08-12T18:17:10.165177447-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165014000}}}}
{"time":"2025-08-12T18:17:10.165180824-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":4}
{"time":"2025-08-12T18:17:10.165190048-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165029000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}}}
{"time":"2025-08-12T18:17:10.165194327-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":5}
{"time":"2025-08-12T18:17:10.165200436-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165042000}}}}
{"time":"2025-08-12T18:17:10.165203219-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":6}
{"time":"2025-08-12T18:17:10.165210331-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}}}
{"time":"2025-08-12T18:17:10.165213874-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":7}
{"time":"2025-08-12T18:17:10.165220131-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165068000}}}}
{"time":"2025-08-12T18:17:10.165223243-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":8}
{"time":"2025-08-12T18:17:10.16523043-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165081000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}}}
{"time":"2025-08-12T18:17:10.165233963-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":9}
{"time":"2025-08-12T18:17:10.165224506-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164854000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165246593-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164920000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165255282-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164968000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165259479-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164985000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165262573-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165000000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165266104-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165014000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165268921-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165029000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165273081-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165042000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165285554-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165289015-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165068000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165291864-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165081000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165240415-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165093000}}}}
{"time":"2025-08-12T18:17:10.165309655-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165309243-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164854000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165319739-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164920000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165322948-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164968000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165325259-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165105000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}}}
{"time":"2025-08-12T18:17:10.165332571-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165331089-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":22,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164854000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165343468-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165119000}}}}
{"time":"2025-08-12T18:17:10.165348187-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:10.165350465-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":23,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164920000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165354663-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":24,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164968000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165356034-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:17:10.165360451-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:10.165367284-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165146000}}}}
{"time":"2025-08-12T18:17:10.165371028-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":3}
{"time":"2025-08-12T18:17:10.165385116-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165159000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}}}
{"time":"2025-08-12T18:17:10.165391595-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":4}
{"time":"2025-08-12T18:17:10.165380725-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165093000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165399086-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165174000}}}}
{"time":"2025-08-12T18:17:10.165404109-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":4}
{"time":"2025-08-12T18:17:10.165399128-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165105000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165407766-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165119000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165412374-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165185000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}}}
{"time":"2025-08-12T18:17:10.165411699-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165417118-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":3}
{"time":"2025-08-12T18:17:10.165417922-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165146000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165421732-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165159000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165426927-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165174000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165326304-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164985000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165437592-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165196000}}}}
{"time":"2025-08-12T18:17:10.165441984-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165441739-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165000000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165447389-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165014000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.16545051-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165029000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.16545234-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165206000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}}}
{"time":"2025-08-12T18:17:10.165454202-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165042000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165457478-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:10.165457984-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165462199-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165068000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165465575-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165218000}}}}
{"time":"2025-08-12T18:17:10.165470794-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:10.165470736-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":25,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":164985000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165479408-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":26,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165000000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165465013-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165081000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165507397-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":27,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165014000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165513082-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":28,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165029000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165510067-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165093000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.16550769-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165185000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165552389-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":29,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165042000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165557693-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":30,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165054000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165558644-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165196000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165566346-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165206000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165571588-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165218000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165539221-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165105000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165594711-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165119000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165600919-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":31,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165068000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165601242-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165605464-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":32,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165081000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165478336-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165229000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}}}
{"time":"2025-08-12T18:17:10.165623274-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.16563268-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165240000}}}}
{"time":"2025-08-12T18:17:10.165636829-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165653338-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165250000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}}}
{"time":"2025-08-12T18:17:10.165658502-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:10.165665442-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165261000}}}}
{"time":"2025-08-12T18:17:10.16566683-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":33,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165093000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165672327-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165229000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165669228-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:10.165680127-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165240000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.1656067-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165146000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165690844-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165159000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165676011-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":34,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165105000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165683575-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165250000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165777837-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165261000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165693637-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165277000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}}}
{"time":"2025-08-12T18:17:10.165806143-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165815998-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":35,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165119000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165834096-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165277000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165699329-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165174000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.16584342-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":36,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165829827-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165288000}}}}
{"time":"2025-08-12T18:17:10.165858022-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165873148-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165298000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:17:10.165877607-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.165883941-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165308000}}}}
{"time":"2025-08-12T18:17:10.165888204-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:10.165895096-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165319000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:17:10.165899101-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":2}
{"time":"2025-08-12T18:17:10.16590368-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":37,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165146000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165906006-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165330000}}}}
{"time":"2025-08-12T18:17:10.165907558-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":38,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165159000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165846662-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165185000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165946485-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165196000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165954326-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165206000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165959663-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165218000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165964492-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165229000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165965895-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":39,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165174000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165972416-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":40,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165185000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165969022-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165240000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165984842-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165250000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165990625-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165261000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165910152-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":3}
{"time":"2025-08-12T18:17:10.165998612-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165277000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.165911806-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165288000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166011263-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":41,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165196000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166013466-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165340000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}}}
{"time":"2025-08-12T18:17:10.166011316-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165298000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166023404-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165308000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166029067-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":1}
{"time":"2025-08-12T18:17:10.166027862-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165319000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166026903-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165288000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166034475-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165330000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166037975-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165298000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166047041-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165352000}}}}
{"time":"2025-08-12T18:17:10.166055726-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.166039475-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165340000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.16606202-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165352000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166065042-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165371000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}}}
{"time":"2025-08-12T18:17:10.166046-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165308000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166070897-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.166074396-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165319000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166080216-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165387000}}}}
{"time":"2025-08-12T18:17:10.166080143-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165330000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166084415-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.166084102-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165340000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166087814-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165352000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166088827-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165371000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166094373-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165387000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166097777-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165371000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166017438-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":42,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165206000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166101644-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165387000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166165718-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":43,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165218000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166172006-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":44,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165229000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166219969-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":45,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165240000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166223462-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":46,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165250000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166283986-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":47,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165261000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166287034-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":48,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165277000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166324347-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":49,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165288000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166328694-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":50,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165298000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.16635365-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":51,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165308000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166356403-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":52,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165319000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166386619-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":53,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165330000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166389358-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":54,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165340000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166416857-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":55,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165352000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166420279-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":56,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165371000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.166439559-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":57,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":165387000}}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.168673996-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":168571000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}}}
{"time":"2025-08-12T18:17:10.168713876-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:44126\")","buffer":0}
{"time":"2025-08-12T18:17:10.168724805-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":168571000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.168742408-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":168571000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.168761896-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":58,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047830,"nanos":168571000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:44126"},"_info":{"stream_id":"agqrsft4"}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.169490516-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:10.178155173-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:17:10.598466886-07:00","level":"DEBUG","msg":"handling record","record":{"Exit":{"exit_code":1}}}
{"time":"2025-08-12T18:17:10.598544353-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Exit); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:17:10.598551534-07:00","level":"INFO","msg":"stream: closing","id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598556529-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1}},"control":{"always_send":true}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598579075-07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-12T18:17:10.598594583-07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-12T18:17:10.598593535-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"2"}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598652917-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1,"runtime":2}},"control":{"always_send":true}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598663432-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":59,"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"2"}]}}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598679006-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:17:10.59868841-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Record":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"2"}]}}}
{"time":"2025-08-12T18:17:10.598716018-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":60,"RecordType":{"Exit":{"exit_code":1,"runtime":2}},"control":{"always_send":true}}},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598756438-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(1))","buffer":0}
{"time":"2025-08-12T18:17:10.59876525-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598780178-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598783567-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.5987989-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(2))","buffer":0}
{"time":"2025-08-12T18:17:10.598802074-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598814042-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.598816305-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.602311263-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:17:10.602461132-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:10.64926048-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["output.log","wandb-summary.json"]}
{"time":"2025-08-12T18:17:10.64935194-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:10.728313747-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(3))","buffer":0}
{"time":"2025-08-12T18:17:10.728327911-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.728335657-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.728340153-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.728345292-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(4))","buffer":0}
{"time":"2025-08-12T18:17:10.728347952-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.728350189-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.728352462-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/output.log, Name: output.log, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=c31c6faf2087ec25eaee58c43258d2c1678b2366f08586d7d6347b8d9418aa7eab850094a718f9596f3366561649b483571155cbb1e997e11c3f9e232868b4ff591552a69fe4f5acc16075bcf237020bea15aa3709579749129d3d9f95548987e6f4375e4ceee9ee82f9f1deab179cd94f5d2a47b280939fa8dd911d3cdcb863aaf92bad1bad37d38de08df2f0f99c114edfeee67c2c375fe0ebdb6443a8b746b84b0ad4e6251d9649adf38b847588817b8f60e9803fce4ae9404274111fe1f05f80f022869cf3279c2ceb5ec0c4335dee9447a931679ed674d2825cdf0d2c740dc1962bfa39df3b2e85e12e0c8bd2b7d0f0a93b47af835ed9b7d45eac19149b&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:17:10.********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/wandb-summary.json, Name: wandb-summary.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=a9af40c50ab04cf60e03fedd0238e97f3c9e57c3671b198393947ee7ca394e8469b9f20bc12c8621a5c4b3d4fc8c65253b87022e4b7bac306054a193545f56064b8ccc109fb165b6a623031597ec08d22ee3eba1d691fc4e58cf4d47183e8f892520d4d28b0b6f7c38d8b9ce87a44fa8782aff7a3ae08b5594993958b3f0fbdb47e9f386df95501fbe91b5642fe08fd16ccee2d3d71546f8074086aee9613ef8a7465791e9bda26a59d2387e1312286848ba4ba5159208c29d958a46290b923335c50dbb6e0aaa95fd65c2eb61b1547685586eb1191aa7d986865551d8250d8413d236fb118e3a4853400fe6b0031255e4744f671e7bb72756b12ce0a22be960&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:17:10.********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/wandb-summary.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=a9af40c50ab04cf60e03fedd0238e97f3c9e57c3671b198393947ee7ca394e8469b9f20bc12c8621a5c4b3d4fc8c65253b87022e4b7bac306054a193545f56064b8ccc109fb165b6a623031597ec08d22ee3eba1d691fc4e58cf4d47183e8f892520d4d28b0b6f7c38d8b9ce87a44fa8782aff7a3ae08b5594993958b3f0fbdb47e9f386df95501fbe91b5642fe08fd16ccee2d3d71546f8074086aee9613ef8a7465791e9bda26a59d2387e1312286848ba4ba5159208c29d958a46290b923335c50dbb6e0aaa95fd65c2eb61b1547685586eb1191aa7d986865551d8250d8413d236fb118e3a4853400fe6b0031255e4744f671e7bb72756b12ce0a22be960&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/output.log","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=c31c6faf2087ec25eaee58c43258d2c1678b2366f08586d7d6347b8d9418aa7eab850094a718f9596f3366561649b483571155cbb1e997e11c3f9e232868b4ff591552a69fe4f5acc16075bcf237020bea15aa3709579749129d3d9f95548987e6f4375e4ceee9ee82f9f1deab179cd94f5d2a47b280939fa8dd911d3cdcb863aaf92bad1bad37d38de08df2f0f99c114edfeee67c2c375fe0ebdb6443a8b746b84b0ad4e6251d9649adf38b847588817b8f60e9803fce4ae9404274111fe1f05f80f022869cf3279c2ceb5ec0c4335dee9447a931679ed674d2825cdf0d2c740dc1962bfa39df3b2e85e12e0c8bd2b7d0f0a93b47af835ed9b7d45eac19149b&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=c31c6faf2087ec25eaee58c43258d2c1678b2366f08586d7d6347b8d9418aa7eab850094a718f9596f3366561649b483571155cbb1e997e11c3f9e232868b4ff591552a69fe4f5acc16075bcf237020bea15aa3709579749129d3d9f95548987e6f4375e4ceee9ee82f9f1deab179cd94f5d2a47b280939fa8dd911d3cdcb863aaf92bad1bad37d38de08df2f0f99c114edfeee67c2c375fe0ebdb6443a8b746b84b0ad4e6251d9649adf38b847588817b8f60e9803fce4ae9404274111fe1f05f80f022869cf3279c2ceb5ec0c4335dee9447a931679ed674d2825cdf0d2c740dc1962bfa39df3b2e85e12e0c8bd2b7d0f0a93b47af835ed9b7d45eac19149b&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=a9af40c50ab04cf60e03fedd0238e97f3c9e57c3671b198393947ee7ca394e8469b9f20bc12c8621a5c4b3d4fc8c65253b87022e4b7bac306054a193545f56064b8ccc109fb165b6a623031597ec08d22ee3eba1d691fc4e58cf4d47183e8f892520d4d28b0b6f7c38d8b9ce87a44fa8782aff7a3ae08b5594993958b3f0fbdb47e9f386df95501fbe91b5642fe08fd16ccee2d3d71546f8074086aee9613ef8a7465791e9bda26a59d2387e1312286848ba4ba5159208c29d958a46290b923335c50dbb6e0aaa95fd65c2eb61b1547685586eb1191aa7d986865551d8250d8413d236fb118e3a4853400fe6b0031255e4744f671e7bb72756b12ce0a22be960&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["config.yaml"]}
{"time":"2025-08-12T18:17:10.********-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/config.yaml, Name: config.yaml, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=2587801176dd429505238ca7f6e20f2eecc61e95288d6db14711921a86187c521ca0d0fd97f7a7e9260b49f568706601e85099c082b4481f2327cf0afeab980bf7da28d423fbeb9dddae2f1c2e9bafa4eb1c4e01f1ec9b12c2b90b028872e1dfd492ced23ab5dce264cf19669ff8a2602fccff94e0ebf19f301c9f33ffd36d0599208e6092f90f1b75421dbf2a03eac97fe3fa1238e4a6c65b36faae72e2f4c44affd0ff6939638aa9a0185abe221dc972c13fd1f97a006352b6d1161b94ce3189eb527f7dc4e298277ab94293eaab998eb59859998a65146a873526fe3366e4ce7498368b17a4dd386de0b3faaf8eceb46faf4543f9aec91acee26df10d2ce1&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181707-agqrsft4/files/config.yaml","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=2587801176dd429505238ca7f6e20f2eecc61e95288d6db14711921a86187c521ca0d0fd97f7a7e9260b49f568706601e85099c082b4481f2327cf0afeab980bf7da28d423fbeb9dddae2f1c2e9bafa4eb1c4e01f1ec9b12c2b90b028872e1dfd492ced23ab5dce264cf19669ff8a2602fccff94e0ebf19f301c9f33ffd36d0599208e6092f90f1b75421dbf2a03eac97fe3fa1238e4a6c65b36faae72e2f4c44affd0ff6939638aa9a0185abe221dc972c13fd1f97a006352b6d1161b94ce3189eb527f7dc4e298277ab94293eaab998eb59859998a65146a873526fe3366e4ce7498368b17a4dd386de0b3faaf8eceb46faf4543f9aec91acee26df10d2ce1&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/agqrsft4/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011710Z&X-Goog-Expires=86399&X-Goog-Signature=2587801176dd429505238ca7f6e20f2eecc61e95288d6db14711921a86187c521ca0d0fd97f7a7e9260b49f568706601e85099c082b4481f2327cf0afeab980bf7da28d423fbeb9dddae2f1c2e9bafa4eb1c4e01f1ec9b12c2b90b028872e1dfd492ced23ab5dce264cf19669ff8a2602fccff94e0ebf19f301c9f33ffd36d0599208e6092f90f1b75421dbf2a03eac97fe3fa1238e4a6c65b36faae72e2f4c44affd0ff6939638aa9a0185abe221dc972c13fd1f97a006352b6d1161b94ce3189eb527f7dc4e298277ab94293eaab998eb59859998a65146a873526fe3366e4ce7498368b17a4dd386de0b3faaf8eceb46faf4543f9aec91acee26df10d2ce1&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"output.log"}}
{"time":"2025-08-12T18:17:10.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-summary.json"}}
{"time":"2025-08-12T18:17:11.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"config.yaml"}}
{"time":"2025-08-12T18:17:11.045941968-07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-12T18:17:11.045950935-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(5))","buffer":0}
{"time":"2025-08-12T18:17:11.045959194-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.045964701-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.045969428-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.045974016-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"ExitCode":1}}
{"time":"2025-08-12T18:17:11.046100898-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":0,\"content\":[\"{\\\"ts\\\":\\\"2025-08-13T01:17:08.906271\\\",\\\"content\\\":\\\"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/agqrsft4/config.yaml\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:08.906315\\\",\\\"content\\\":\\\"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:08.906372\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:09.720899\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:09.819786\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\\\\\\\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\\\\\\\"].\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:09.819959\\\",\\\"content\\\":\\\"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048504\\\",\\\"content\\\":\\\"\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048507\\\",\\\"content\\\":\\\"  | Name             | Type                 | Params | Mode\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048514\\\",\\\"content\\\":\\\"------------------------------------------------------------------\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048531\\\",\\\"content\\\":\\\"0 | NMSE             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048537\\\",\\\"content\\\":\\\"1 | SSIM             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048544\\\",\\\"content\\\":\\\"2 | PSNR             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048550\\\",\\\"content\\\":\\\"3 | ValLoss          | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048564\\\",\\\"content\\\":\\\"4 | TotExamples      | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048571\\\",\\\"content\\\":\\\"5 | TotSliceExamples | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048582\\\",\\\"content\\\":\\\"6 | promptmr         | PromptMR             | 82.3 M | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048598\\\",\\\"content\\\":\\\"7 | loss             | SSIMLoss             | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048603\\\",\\\"content\\\":\\\"------------------------------------------------------------------\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048632\\\",\\\"content\\\":\\\"72.0 M    Trainable params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048643\\\",\\\"content\\\":\\\"10.4 M    Non-trainable params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048649\\\",\\\"content\\\":\\\"82.3 M    Total params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.048653\\\",\\\"content\\\":\\\"329.273   Total estimated model params size (MB)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165338\\\",\\\"content\\\":\\\"Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165357\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165365\\\",\\\"content\\\":\\\"    run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165486\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165501\\\",\\\"content\\\":\\\"    cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165518\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165545\\\",\\\"content\\\":\\\"    self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165560\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165596\\\",\\\"content\\\":\\\"    fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165610\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165659\\\",\\\"content\\\":\\\"    call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165714\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165790\\\",\\\"content\\\":\\\"    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165850\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165892\\\",\\\"content\\\":\\\"    return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165917\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165954\\\",\\\"content\\\":\\\"    self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.165975\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 977, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166000\\\",\\\"content\\\":\\\"    self._checkpoint_connector.restore_training_state()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166105\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 298, in restore_training_state\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166152\\\",\\\"content\\\":\\\"    self.restore_optimizers_and_schedulers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166175\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 368, in restore_optimizers_and_schedulers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166210\\\",\\\"content\\\":\\\"    self.restore_optimizers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166228\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 383, in restore_optimizers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166272\\\",\\\"content\\\":\\\"    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166293\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 376, in load_optimizer_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166316\\\",\\\"content\\\":\\\"    optimizer.load_state_dict(opt_state)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166332\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\\\\\\\", line 31, in inner\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166349\\\",\\\"content\\\":\\\"    return disable_fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166358\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\\\\\\\", line 600, in _fn\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166382\\\",\\\"content\\\":\\\"    return fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166391\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 854, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166409\\\",\\\"content\\\":\\\"    raise ValueError(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.166424\\\",\\\"content\\\":\\\"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168773\\\",\\\"content\\\":\\\"[rank0]: Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168788\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168798\\\",\\\"content\\\":\\\"[rank0]:     run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168801\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168806\\\",\\\"content\\\":\\\"[rank0]:     cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168812\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168841\\\",\\\"content\\\":\\\"[rank0]:     self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168847\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168864\\\",\\\"content\\\":\\\"[rank0]:     fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168867\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168886\\\",\\\"content\\\":\\\"[rank0]:     call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168891\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168935\\\",\\\"content\\\":\\\"[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168950\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168982\\\",\\\"content\\\":\\\"[rank0]:     return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.168989\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169013\\\",\\\"content\\\":\\\"[rank0]:     self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169022\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 977, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169043\\\",\\\"content\\\":\\\"[rank0]:     self._checkpoint_connector.restore_training_state()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169052\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 298, in restore_training_state\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169081\\\",\\\"content\\\":\\\"[rank0]:     self.restore_optimizers_and_schedulers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169088\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 368, in restore_optimizers_and_schedulers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169121\\\",\\\"content\\\":\\\"[rank0]:     self.restore_optimizers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169126\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 383, in restore_optimizers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169157\\\",\\\"content\\\":\\\"[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169170\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 376, in load_optimizer_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169201\\\",\\\"content\\\":\\\"[rank0]:     optimizer.load_state_dict(opt_state)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169212\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\\\\\\\", line 31, in inner\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169228\\\",\\\"content\\\":\\\"[rank0]:     return disable_fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169234\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\\\\\\\", line 600, in _fn\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169250\\\",\\\"content\\\":\\\"[rank0]:     return fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169254\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 854, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169271\\\",\\\"content\\\":\\\"[rank0]:     raise ValueError(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:17:10.169274\\\",\\\"content\\\":\\\"[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\\\"}\"]},\"wandb-summary.json\":{\"offset\":0,\"content\":[\"{\\\"_wandb\\\":{\\\"runtime\\\":2}}\"]}},\"uploaded\":[\"wandb-summary.json\",\"config.yaml\",\"requirements.txt\",\"output.log\"],\"complete\":true,\"exitcode\":1}"}
{"time":"2025-08-12T18:17:11.046173832-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/agqrsft4/file_stream"}
{"time":"2025-08-12T18:17:11.17220832-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":1,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:17:11.172467016-07:00","level":"DEBUG","msg":"filestream: closed"}
{"time":"2025-08-12T18:17:11.172496625-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(6))","buffer":0}
{"time":"2025-08-12T18:17:11.17251273-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.172524543-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.172538754-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.172553804-07:00","level":"INFO","msg":"handler: closed","stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.172557631-07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.172643172-07:00","level":"INFO","msg":"sender: closed","stream_id":"agqrsft4"}
{"time":"2025-08-12T18:17:11.1742878-07:00","level":"INFO","msg":"stream: closed","id":"agqrsft4"}
