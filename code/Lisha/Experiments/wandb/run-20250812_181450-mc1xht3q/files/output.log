Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/mc1xht3q/config.yaml
Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3
/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: ["ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}"].
LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]

  | Name             | Type                 | Params | Mode
------------------------------------------------------------------
0 | NMSE             | DistributedMetricSum | 0      | train
1 | SSIM             | DistributedMetricSum | 0      | train
2 | PSNR             | DistributedMetricSum | 0      | train
3 | ValLoss          | DistributedMetricSum | 0      | train
4 | TotExamples      | DistributedMetricSum | 0      | train
5 | TotSliceExamples | DistributedMetricSum | 0      | train
6 | promptmr         | PromptMR             | 82.3 M | train
7 | loss             | SSIMLoss             | 0      | train
------------------------------------------------------------------
72.0 M    Trainable params
10.4 M    Non-trainable params
82.3 M    Total params
329.273   Total estimated model params size (MB)
Traceback (most recent call last):
  File "main.py", line 244, in <module>
    run_cli()
  File "main.py", line 236, in run_cli
    cli = CustomLightningCLI(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 394, in __init__
    self._run_subcommand(self.subcommand)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 701, in _run_subcommand
    fn(**fn_kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 543, in fit
    call._call_and_handle_interrupt(
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 43, in _call_and_handle_interrupt
    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
    return function(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 579, in _fit_impl
    self._run(model, ckpt_path=ckpt_path)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 977, in _run
    self._checkpoint_connector.restore_training_state()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 298, in restore_training_state
    self.restore_optimizers_and_schedulers()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 368, in restore_optimizers_and_schedulers
    self.restore_optimizers()
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 383, in restore_optimizers
    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 376, in load_optimizer_state_dict
    optimizer.load_state_dict(opt_state)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py", line 31, in inner
    return disable_fn(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py", line 600, in _fn
    return fn(*args, **kwargs)
  File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py", line 854, in load_state_dict
    raise ValueError(
ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group
[rank0]: Traceback (most recent call last):
[rank0]:   File "main.py", line 244, in <module>
[rank0]:     run_cli()
[rank0]:   File "main.py", line 236, in run_cli
[rank0]:     cli = CustomLightningCLI(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 394, in __init__
[rank0]:     self._run_subcommand(self.subcommand)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py", line 701, in _run_subcommand
[rank0]:     fn(**fn_kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 543, in fit
[rank0]:     call._call_and_handle_interrupt(
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py", line 43, in _call_and_handle_interrupt
[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py", line 105, in launch
[rank0]:     return function(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 579, in _fit_impl
[rank0]:     self._run(model, ckpt_path=ckpt_path)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py", line 977, in _run
[rank0]:     self._checkpoint_connector.restore_training_state()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 298, in restore_training_state
[rank0]:     self.restore_optimizers_and_schedulers()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 368, in restore_optimizers_and_schedulers
[rank0]:     self.restore_optimizers()
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py", line 383, in restore_optimizers
[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py", line 376, in load_optimizer_state_dict
[rank0]:     optimizer.load_state_dict(opt_state)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py", line 31, in inner
[rank0]:     return disable_fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py", line 600, in _fn
[rank0]:     return fn(*args, **kwargs)
[rank0]:   File "/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py", line 854, in load_state_dict
[rank0]:     raise ValueError(
[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group
