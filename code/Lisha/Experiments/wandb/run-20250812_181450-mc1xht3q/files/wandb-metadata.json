{"os": "Linux-4.18.0-348.el8.x86_64-x86_64-with-glibc2.17", "python": "CPython 3.8.20", "startedAt": "2025-08-13T01:14:50.083482Z", "args": ["fit", "--config", "configs/train/pmr-plus/cmr25-cardiac-upd.yaml", "--trainer.accelerator=gpu", "--trainer.strategy=ddp", "--trainer.devices=4", "--trainer.max_epochs=50", "--data.init_args.batch_size=1", "--trainer.logger.save_dir=/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments", "--trainer.logger.init_args.project=cmr2025_task3", "--trainer.logger.init_args.tags=[baseline,promptmr_plus,comparison]", "--trainer.logger.init_args.name=pmrplusXL_l1testime", "--trainer.logger.init_args.log_model=all", "--trainer.logger.init_args.offline=false"], "program": "main.py", "codePath": "main.py", "email": "<EMAIL>", "root": "/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments", "host": "esplhpc-cp088", "executable": "/home/<USER>/miniconda3/envs/promptmr/bin/python", "codePathLocal": "main.py", "cpu_count": 96, "cpu_count_logical": 96, "gpu": "NVIDIA H100 80GB HBM3", "gpu_count": 4, "disk": {"/": {"total": "942552190976", "used": "18732400640"}}, "memory": {"total": "2164152020992"}, "cpu": {"count": 96, "countLogical": 96}, "gpu_nvidia": [{"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "***********", "cudaCores": 16896, "architecture": "<PERSON>"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "***********", "cudaCores": 16896, "architecture": "<PERSON>"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "***********", "cudaCores": 16896, "architecture": "<PERSON>"}, {"name": "NVIDIA H100 80GB HBM3", "memoryTotal": "***********", "cudaCores": 16896, "architecture": "<PERSON>"}], "slurm": {"cluster_name": "slurm-compbio", "conf": "/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf", "cpu_bind": "quiet,mask_cpu:0x00000000000F000000000017", "cpu_bind_list": "0x00000000000F000000000017", "cpu_bind_type": "mask_cpu:", "cpu_bind_verbose": "quiet", "cpus_on_node": "8", "cpus_per_task": "8", "distribution": "cyclic", "gpus_on_node": "4", "gtids": "0", "job_account": "user", "job_cpus_per_node": "8", "job_end_time": "**********", "job_gid": "23023", "job_gpus": "0,1,2,3", "job_id": "2155068", "job_name": "cmr_baselineXL_task3", "job_nodelist": "esplhpc-cp088", "job_num_nodes": "1", "job_partition": "gpu", "job_qos": "normal", "job_start_time": "**********", "job_uid": "1235884", "job_user": "zengl2", "jobid": "2155068", "launch_node_ipaddr": "************", "localid": "0", "mem_per_node": "131072", "nnodes": "1", "nodeid": "0", "nodelist": "esplhpc-cp088", "nprocs": "1", "ntasks": "1", "prio_process": "0", "procid": "0", "srun_comm_host": "************", "srun_comm_port": "43019", "step_gpus": "0,1,2,3", "step_id": "0", "step_launcher_port": "43019", "step_nodelist": "esplhpc-cp088", "step_num_nodes": "1", "step_num_tasks": "1", "step_tasks_per_node": "1", "stepid": "0", "submit_dir": "/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3", "submit_host": "esplhpccompbio-lv02", "task_pid": "1710556", "tasks_per_node": "1", "topology_addr": "esplhpc-cp088", "topology_addr_pattern": "node", "tres_per_task": "cpu:8", "umask": "0022"}, "cudaVersion": "12.4"}