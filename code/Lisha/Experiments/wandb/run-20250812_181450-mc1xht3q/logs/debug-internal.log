{"time":"2025-08-12T18:14:50.089776213-07:00","level":"INFO","msg":"stream: starting","core version":"0.19.9","symlink path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/logs/debug-core.log"}
{"time":"2025-08-12T18:14:50.089845909-07:00","level":"DEBUG","msg":"monitor: sampling interval: 15s"}
{"time":"2025-08-12T18:14:50.204213778-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.287914557-07:00","level":"INFO","msg":"created new stream","id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.28793706-07:00","level":"INFO","msg":"stream: started","id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.287951798-07:00","level":"INFO","msg":"writer: Do: started","stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.287966644-07:00","level":"INFO","msg":"handler: started","stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.287973514-07:00","level":"INFO","msg":"sender: started","stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.288417505-07:00","level":"DEBUG","msg":"handling record","record":{"Header":{}}}
{"time":"2025-08-12T18:14:50.288434766-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Header); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.288458842-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Header":{}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.288803363-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"mc1xht3q","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:14:50.288817742-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(mailbox_slot:\"62pc7ebjd7a6\" connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.288825777-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"mc1xht3q","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"62pc7ebjd7a6","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.290353852-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.290385602-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"mc1xht3q","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"62pc7ebjd7a6","always_send":true,"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.290426063-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":1,"RecordType":{"Header":{"version_info":{"producer":"0.19.9+adfffc113aae8f44d4ef1f426555826b41883316","min_consumer":"0.40.0"}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.290432232-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":2,"RecordType":{"Run":{"run_id":"mc1xht3q","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"mailbox_slot":"62pc7ebjd7a6","always_send":true,"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.290546849-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.53799448-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"RunResult":{"run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","storage_id":"UnVuOnYxOm1jMXhodDNxOmNtcjIwMjVfdGFzazM6bGlzaGEtemVuZy1jZWRhcnMtc2luYWk=","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}},"control":{"mailbox_slot":"62pc7ebjd7a6","always_send":true,"connection_id":"127.0.0.1:60224"}}}
{"time":"2025-08-12T18:14:50.540229091-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}}}
{"time":"2025-08-12T18:14:50.540249945-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_RunStart); Control(local:true mailbox_slot:\"gpokt2pog8ei\" connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.54025888-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"gpokt2pog8ei","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.540327675-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"gpokt2pog8ei","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.540379146-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"RunStart":{"run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["baseline","promptmr_plus","comparison"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}}},"control":{"local":true,"mailbox_slot":"gpokt2pog8ei","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.540398957-07:00","level":"DEBUG","msg":"filestream: start","path":""}
{"time":"2025-08-12T18:14:50.540412629-07:00","level":"DEBUG","msg":"filestream: open","path":"files/lisha-zeng-cedars-sinai/cmr2025_task3/mc1xht3q/file_stream"}
{"time":"2025-08-12T18:14:50.543674943-07:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-08-12T18:14:50.543690905-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":null}},"control":{"local":true,"mailbox_slot":"gpokt2pog8ei","connection_id":"127.0.0.1:60224"}}}
{"time":"2025-08-12T18:14:50.543741479-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.54378846-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":3,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.571078931-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_Metadata); Control(<nil>)","buffer":0}
{"time":"2025-08-12T18:14:50.571091806-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"Metadata":{"cpu_count":96,"cpu_count_logical":96,"gpu_type":"NVIDIA H100 80GB HBM3","gpu_count":4,"disk":{"/":{"total":942552190976,"used":18732400640}},"memory":{"total":2164152020992},"cpu":{"count":96,"count_logical":96},"gpu_nvidia":[{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"},{"name":"NVIDIA H100 80GB HBM3","memory_total":***********,"cuda_cores":16896,"architecture":"Hopper"}],"slurm":{"cluster_name":"slurm-compbio","conf":"/cm/shared/apps/slurm/var/etc/slurm-compbio/slurm.conf","cpu_bind":"quiet,mask_cpu:0x00000000000F000000000017","cpu_bind_list":"0x00000000000F000000000017","cpu_bind_type":"mask_cpu:","cpu_bind_verbose":"quiet","cpus_on_node":"8","cpus_per_task":"8","distribution":"cyclic","gpus_on_node":"4","gtids":"0","job_account":"user","job_cpus_per_node":"8","job_end_time":"**********","job_gid":"23023","job_gpus":"0,1,2,3","job_id":"2155068","job_name":"cmr_baselineXL_task3","job_nodelist":"esplhpc-cp088","job_num_nodes":"1","job_partition":"gpu","job_qos":"normal","job_start_time":"**********","job_uid":"1235884","job_user":"zengl2","jobid":"2155068","launch_node_ipaddr":"************","localid":"0","mem_per_node":"131072","nnodes":"1","nodeid":"0","nodelist":"esplhpc-cp088","nprocs":"1","ntasks":"1","prio_process":"0","procid":"0","srun_comm_host":"************","srun_comm_port":"43019","step_gpus":"0,1,2,3","step_id":"0","step_launcher_port":"43019","step_nodelist":"esplhpc-cp088","step_num_nodes":"1","step_num_tasks":"1","step_tasks_per_node":"1","stepid":"0","submit_dir":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3","submit_host":"esplhpccompbio-lv02","task_pid":"1710556","tasks_per_node":"1","topology_addr":"esplhpc-cp088","topology_addr_pattern":"node","tres_per_task":"cpu:8","umask":"0022"},"cuda_version":"12.4"}}}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.574733488-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.57474347-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":4,"RecordType":{"Files":{"files":[{"path":"wandb-metadata.json","type":1}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.594200675-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["wandb-metadata.json"]}
{"time":"2025-08-12T18:14:50.594286731-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/wandb-metadata.json, Name: wandb-metadata.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011450Z&X-Goog-Expires=86399&X-Goog-Signature=3367eadd3986845c7267a2d8b3af5d40377be077ed552f9034a7f313859d224712002bb729e5444d526a33797314430e2e54568ac8be22ff0af57e0a8fd42e752c32d16cab99f724e9172963014fbd58aeae1b92ab202e0d209b2a8d3d01b4229bc54d10849a8f64a76c7ad8288b617bcb045027babd5619ec218c986557d3e5323ba6f4c905bcdaaf16c98a7a513f8766f969a172797fafcb66e16f75dff2f41f7cb163c7941f65faf5e1132d02c720066375c355897659ad2e00f1a25998b78f1ee51ddba57bcc09888b7288e2677f046203cb351317adfc311223ffb9813d33d680311837b3bf4da438de65e86d18e0a1863700ab26fee04ef963f2cb0134&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/wandb-metadata.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011450Z&X-Goog-Expires=86399&X-Goog-Signature=3367eadd3986845c7267a2d8b3af5d40377be077ed552f9034a7f313859d224712002bb729e5444d526a33797314430e2e54568ac8be22ff0af57e0a8fd42e752c32d16cab99f724e9172963014fbd58aeae1b92ab202e0d209b2a8d3d01b4229bc54d10849a8f64a76c7ad8288b617bcb045027babd5619ec218c986557d3e5323ba6f4c905bcdaaf16c98a7a513f8766f969a172797fafcb66e16f75dff2f41f7cb163c7941f65faf5e1132d02c720066375c355897659ad2e00f1a25998b78f1ee51ddba57bcc09888b7288e2677f046203cb351317adfc311223ffb9813d33d680311837b3bf4da438de65e86d18e0a1863700ab26fee04ef963f2cb0134&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011450Z&X-Goog-Expires=86399&X-Goog-Signature=3367eadd3986845c7267a2d8b3af5d40377be077ed552f9034a7f313859d224712002bb729e5444d526a33797314430e2e54568ac8be22ff0af57e0a8fd42e752c32d16cab99f724e9172963014fbd58aeae1b92ab202e0d209b2a8d3d01b4229bc54d10849a8f64a76c7ad8288b617bcb045027babd5619ec218c986557d3e5323ba6f4c905bcdaaf16c98a7a513f8766f969a172797fafcb66e16f75dff2f41f7cb163c7941f65faf5e1132d02c720066375c355897659ad2e00f1a25998b78f1ee51ddba57bcc09888b7288e2677f046203cb351317adfc311223ffb9813d33d680311837b3bf4da438de65e86d18e0a1863700ab26fee04ef963f2cb0134&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}}}
{"time":"2025-08-12T18:14:50.680038279-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_PythonPackages); Control(local:true connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.680046772-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"PythonPackages":{"package":[{"name":"async-timeout","version":"5.0.1"},{"name":"scipy","version":"1.10.1"},{"name":"matplotlib","version":"3.7.5"},{"name":"annotated-types","version":"0.7.0"},{"name":"pip","version":"24.2"},{"name":"filelock","version":"3.16.1"},{"name":"networkx","version":"3.1"},{"name":"einops","version":"0.8.1"},{"name":"nvidia-cufft-cu12","version":"*********"},{"name":"pytorch-lightning","version":"2.0.0"},{"name":"nvidia-cusolver-cu12","version":"**********"},{"name":"Markdown","version":"3.7"},{"name":"oauthlib","version":"3.2.2"},{"name":"pyasn1","version":"0.6.1"},{"name":"eval_type_backport","version":"0.2.2"},{"name":"rsa","version":"4.9"},{"name":"platformdirs","version":"4.3.6"},{"name":"google-auth-oauthlib","version":"1.0.0"},{"name":"lazy_loader","version":"0.4"},{"name":"nvidia-nccl-cu12","version":"2.20.5"},{"name":"torchvision","version":"0.13.0"},{"name":"attrs","version":"25.3.0"},{"name":"urllib3","version":"2.2.3"},{"name":"nvidia-cuda-cupti-cu12","version":"12.1.105"},{"name":"lightning-utilities","version":"0.11.9"},{"name":"pydantic","version":"2.10.6"},{"name":"nvidia-curand-cu12","version":"**********"},{"name":"pydantic_core","version":"2.27.2"},{"name":"PyWavelets","version":"1.4.1"},{"name":"packaging","version":"24.2"},{"name":"python-dateutil","version":"2.9.0.post0"},{"name":"certifi","version":"2024.8.30"},{"name":"PySocks","version":"1.7.1"},{"name":"nvidia-nvjitlink-cu12","version":"12.8.93"},{"name":"importlib_resources","version":"6.4.5"},{"name":"cachetools","version":"5.5.2"},{"name":"kiwisolver","version":"1.4.7"},{"name":"tifffile","version":"2023.7.10"},{"name":"tensorboard-data-server","version":"0.7.2"},{"name":"zipp","version":"3.20.2"},{"name":"torchmetrics","version":"1.5.2"},{"name":"pyparsing","version":"3.1.4"},{"name":"Jinja2","version":"3.1.6"},{"name":"nvidia-cudnn-cu12","version":"********"},{"name":"protobuf","version":"5.29.4"},{"name":"jsonargparse","version":"4.38.0"},{"name":"Brotli","version":"1.0.9"},{"name":"idna","version":"3.7"},{"name":"typing_extensions","version":"4.13.2"},{"name":"mkl-random","version":"1.2.4"},{"name":"sympy","version":"1.13.3"},{"name":"contourpy","version":"1.1.1"},{"name":"mkl-fft","version":"1.3.8"},{"name":"docker-pycreds","version":"0.4.0"},{"name":"setuptools","version":"75.1.0"},{"name":"aiosignal","version":"1.3.1"},{"name":"numpy","version":"1.24.3"},{"name":"pyasn1_modules","version":"0.4.2"},{"name":"pillow","version":"10.4.0"},{"name":"absl-py","version":"2.2.2"},{"name":"mkl-service","version":"2.4.0"},{"name":"multidict","version":"6.1.0"},{"name":"six","version":"1.17.0"},{"name":"lightning","version":"2.3.3"},{"name":"fsspec","version":"2025.3.0"},{"name":"mpmath","version":"1.3.0"},{"name":"gitdb","version":"4.0.12"},{"name":"wandb","version":"0.19.9"},{"name":"fonttools","version":"4.57.0"},{"name":"psutil","version":"7.0.0"},{"name":"nvidia-cuda-nvrtc-cu12","version":"12.1.105"},{"name":"frozenlist","version":"1.5.0"},{"name":"aiohappyeyeballs","version":"2.4.4"},{"name":"PyYAML","version":"6.0.2"},{"name":"requests","version":"2.32.3"},{"name":"yarl","version":"1.15.2"},{"name":"typeshed_client","version":"2.7.0"},{"name":"smmap","version":"5.0.2"},{"name":"h5py","version":"3.11.0"},{"name":"tqdm","version":"4.67.1"},{"name":"torch","version":"2.4.1"},{"name":"nvidia-cuda-runtime-cu12","version":"12.1.105"},{"name":"triton","version":"3.0.0"},{"name":"charset-normalizer","version":"3.3.2"},{"name":"docstring_parser","version":"0.16"},{"name":"Werkzeug","version":"3.0.6"},{"name":"MarkupSafe","version":"2.1.5"},{"name":"wheel","version":"0.44.0"},{"name":"nvidia-cusparse-cu12","version":"12.1.0.106"},{"name":"torchaudio","version":"0.12.0"},{"name":"requests-oauthlib","version":"2.0.0"},{"name":"tensorboard","version":"2.14.0"},{"name":"grpcio","version":"1.70.0"},{"name":"scikit-image","version":"0.21.0"},{"name":"propcache","version":"0.2.0"},{"name":"nvidia-nvtx-cu12","version":"12.1.105"},{"name":"google-auth","version":"2.38.0"},{"name":"sentry-sdk","version":"2.25.1"},{"name":"nvidia-cublas-cu12","version":"12.1.3.1"},{"name":"GitPython","version":"3.1.44"},{"name":"setproctitle","version":"1.3.5"},{"name":"cycler","version":"0.12.1"},{"name":"importlib_metadata","version":"8.5.0"},{"name":"aiohttp","version":"3.10.11"},{"name":"imageio","version":"2.35.1"},{"name":"click","version":"8.1.8"},{"name":"jaraco.context","version":"5.3.0"},{"name":"tomli","version":"2.0.1"},{"name":"jaraco.text","version":"3.12.1"},{"name":"wheel","version":"0.43.0"},{"name":"typing_extensions","version":"4.12.2"},{"name":"importlib_resources","version":"6.4.0"},{"name":"packaging","version":"24.1"},{"name":"platformdirs","version":"4.2.2"},{"name":"autocommand","version":"2.2.2"},{"name":"jaraco.functools","version":"4.0.1"},{"name":"inflect","version":"7.3.1"},{"name":"typeguard","version":"4.3.0"},{"name":"backports.tarfile","version":"1.2.0"},{"name":"more-itertools","version":"10.3.0"},{"name":"zipp","version":"3.19.2"},{"name":"jaraco.collections","version":"5.1.0"},{"name":"importlib_metadata","version":"8.0.0"}]}}}},"control":{"local":true,"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.681726597-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.681753314-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":5,"RecordType":{"Files":{"files":[{"path":"requirements.txt","type":1}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.72079177-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:14:50.720806816-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"9lgubt12urzh\" connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.720834875-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:14:50.720851853-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.720865874-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"NetworkStatus":{}}}}}
{"time":"2025-08-12T18:14:50.720870844-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_NetworkStatus); Control(local:true mailbox_slot:\"6hyf70s7tgtp\" connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:14:50.720897369-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:14:50.720901879-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"71orc3m142f7\" connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:14:50.72088961-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"9lgubt12urzh","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.720911938-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:14:50.720918436-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:14:50.720918158-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.720934241-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"6hyf70s7tgtp","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.720938244-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"71orc3m142f7","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.720943291-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.720962316-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}}}
{"time":"2025-08-12T18:14:50.720967487-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.720961078-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"71orc3m142f7","connection_id":"127.0.0.1:60224"}}}
{"time":"2025-08-12T18:14:50.720980399-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:14:50.72098719-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:50.720993957-07:00","level":"DEBUG","msg":"handling record","record":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}}}
{"time":"2025-08-12T18:14:50.720997445-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Metric); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:14:50.721007735-07:00","level":"DEBUG","msg":"handling record","record":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}
{"time":"2025-08-12T18:14:50.721013541-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Telemetry); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:14:50.721006417-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"9lgubt12urzh","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721025931-07:00","level":"DEBUG","msg":"handling record","record":{"Run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}}}
{"time":"2025-08-12T18:14:50.721033481-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Run); Control(connection_id:\"127.0.0.1:60224\")","buffer":3}
{"time":"2025-08-12T18:14:50.721029576-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721038816-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721044068-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"6hyf70s7tgtp","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721048026-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721061611-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.72104879-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721071961-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721067215-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"9lgubt12urzh","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721078354-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721085233-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.72110467-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721110748-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721115161-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.721113682-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.721119538-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.731918505-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["requirements.txt"]}
{"time":"2025-08-12T18:14:50.731984296-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/requirements.txt, Name: requirements.txt, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011450Z&X-Goog-Expires=86399&X-Goog-Signature=ad5d16126ef484d79c494d7a2cf4ac956c4bb7a9dce88f2a1d62224729464c862b2a88d91dc3be48c3dc3217ff15d69d37b94b7461d81fa615716c4f78cdfced59993ba2122e7527069e627a485fef882684308c3d67c0078b35bb9bfca8ec8add6c8a86e4269785ffa4558d03083b5273def4fc4cc20c3bcbfe861b29304c57b9bcce000fc1f677b43e24de002d9d44a2ce8a0c60323b3476c44b2aed80c9fb39398f33073a3f2457eb9900e0217ac97ba27cde2087f1322fb518415ddd969207d27e8d45cae33fcd0b1f9a73be5177cd1e9cb894c1d4a8ca607f8c4be8132a3b15379328ed386b01c1e5de8d52f5997cca92155cfd847fc0725e9fb10c3156&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/requirements.txt","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011450Z&X-Goog-Expires=86399&X-Goog-Signature=ad5d16126ef484d79c494d7a2cf4ac956c4bb7a9dce88f2a1d62224729464c862b2a88d91dc3be48c3dc3217ff15d69d37b94b7461d81fa615716c4f78cdfced59993ba2122e7527069e627a485fef882684308c3d67c0078b35bb9bfca8ec8add6c8a86e4269785ffa4558d03083b5273def4fc4cc20c3bcbfe861b29304c57b9bcce000fc1f677b43e24de002d9d44a2ce8a0c60323b3476c44b2aed80c9fb39398f33073a3f2457eb9900e0217ac97ba27cde2087f1322fb518415ddd969207d27e8d45cae33fcd0b1f9a73be5177cd1e9cb894c1d4a8ca607f8c4be8132a3b15379328ed386b01c1e5de8d52f5997cca92155cfd847fc0725e9fb10c3156&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/requirements.txt?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011450Z&X-Goog-Expires=86399&X-Goog-Signature=ad5d16126ef484d79c494d7a2cf4ac956c4bb7a9dce88f2a1d62224729464c862b2a88d91dc3be48c3dc3217ff15d69d37b94b7461d81fa615716c4f78cdfced59993ba2122e7527069e627a485fef882684308c3d67c0078b35bb9bfca8ec8add6c8a86e4269785ffa4558d03083b5273def4fc4cc20c3bcbfe861b29304c57b9bcce000fc1f677b43e24de002d9d44a2ce8a0c60323b3476c44b2aed80c9fb39398f33073a3f2457eb9900e0217ac97ba27cde2087f1322fb518415ddd969207d27e8d45cae33fcd0b1f9a73be5177cd1e9cb894c1d4a8ca607f8c4be8132a3b15379328ed386b01c1e5de8d52f5997cca92155cfd847fc0725e9fb10c3156&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:14:50.*********-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":6,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.843742705-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:14:50.843775233-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.843773417-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"9lgubt12urzh","connection_id":"127.0.0.1:60224"}}}
{"time":"2025-08-12T18:14:50.897700277-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-metadata.json"}}
{"time":"2025-08-12T18:14:50.897772421-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"uploaded\":[\"wandb-metadata.json\"]}"}
{"time":"2025-08-12T18:14:50.897795309-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/mc1xht3q/file_stream"}
{"time":"2025-08-12T18:14:50.951390529-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"NetworkStatus":{}}}},"control":{"local":true,"mailbox_slot":"6hyf70s7tgtp","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951409061-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":7,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951438268-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":8,"RecordType":{"Metric":{"name":"trainer/global_step","options":{"defined":true}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951444284-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":9,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951496315-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":10,"RecordType":{"Metric":{"glob_name":"*","step_metric":"trainer/global_step","options":{"step_sync":true,"defined":true}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951500451-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":11,"RecordType":{"Telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951519685-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":12,"RecordType":{"Run":{"run_id":"mc1xht3q","entity":"lisha-zeng-cedars-sinai","project":"cmr2025_task3","config":{"update":[{"key":"_wandb","value_json":"{}"}]},"display_name":"pmrplusXL_l1testime","tags":["cmr25-cardiac"],"host":"esplhpc-cp088","start_time":{"seconds":1755047690,"nanos":83482000},"telemetry":{"imports_init":{"torch":true,"asyncio":true,"lightning":true},"imports_finish":{"torch":true,"asyncio":true,"lightning":true},"feature":{"metric":true,"set_init_name":true,"set_init_tags":true,"set_run_tags":true,"service":true,"core":true,"metric_step_sync":true},"python_version":"3.8.20","cli_version":"0.19.9","env":{"start_spawn":true},"platform":"linux-x86_64"}}},"control":{"always_send":true,"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:50.951569855-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:14:50.979190681-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:14:51.048879542-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48625000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/mc1xht3q/config.yaml"}}}
{"time":"2025-08-12T18:14:51.048894821-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:51.048900769-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48625000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/mc1xht3q/config.yaml"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.04890649-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48625000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/mc1xht3q/config.yaml"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.055715065-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":13,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48625000},"line":"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/mc1xht3q/config.yaml"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.059286741-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:14:51.067053344-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"requirements.txt"}}
{"time":"2025-08-12T18:14:51.089685763-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48706000},"line":"\n"}}}
{"time":"2025-08-12T18:14:51.089703623-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:51.089716301-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":49880000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}}}
{"time":"2025-08-12T18:14:51.089721132-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:14:51.089733588-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":67236000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}}}
{"time":"2025-08-12T18:14:51.089739273-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:14:51.089743074-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48706000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.089748335-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":49880000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.089751645-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":67236000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.089756171-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48706000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.089759709-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":49880000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.08976229-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":67236000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.08976778-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":14,"RecordType":{"OutputRaw":{"output_type":1,"timestamp":{"seconds":**********,"nanos":48706000},"line":"\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.089772924-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":15,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":49880000},"line":"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.089807048-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":16,"RecordType":{"OutputRaw":{"timestamp":{"seconds":**********,"nanos":67236000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:14:51.092387965-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:14:51.102673876-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:00.681138034-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"InternalMessages":{}}}}}
{"time":"2025-08-12T18:15:00.681318085-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_InternalMessages); Control(local:true mailbox_slot:\"4hlly4y7pzxp\" connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:00.681332982-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"InternalMessages":{}}}},"control":{"local":true,"mailbox_slot":"4hlly4y7pzxp","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:00.681373081-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"InternalMessagesResponse":{"messages":{}}}}},"control":{"local":true,"mailbox_slot":"4hlly4y7pzxp","connection_id":"127.0.0.1:60224"}}}
{"time":"2025-08-12T18:15:05.544873705-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:15:05.544906992-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755047705,"nanos":544611630},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.902"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.986"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.537"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.21957142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.352"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.193142857142858"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.666"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.238"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.54497137-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755047705,"nanos":544611630},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.902"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.986"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.537"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.21957142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.352"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.193142857142858"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.666"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.238"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.545006606-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":17,"RecordType":{"Stats":{"timestamp":{"seconds":1755047705,"nanos":544611630},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.902"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.986"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.537"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.21957142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.352"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.193142857142858"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.666"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.238"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.545021693-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:14:50.083482Z","Record":{"timestamp":{"seconds":1755047705,"nanos":544611630},"item":[{"key":"gpu.0.gpu","value_json":"0.0"},{"key":"gpu.0.memory","value_json":"0"},{"key":"gpu.0.memoryAllocated","value_json":"1.8354810627889013"},{"key":"gpu.0.memoryAllocatedBytes","value_json":"1569718272"},{"key":"gpu.0.temp","value_json":"34.0"},{"key":"gpu.0.powerWatts","value_json":"111.902"},{"key":"gpu.0.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.0.powerPercent","value_json":"15.986"},{"key":"gpu.0.smClock","value_json":"1980"},{"key":"gpu.0.memoryClock","value_json":"2619"},{"key":"gpu.0.correctedMemoryErrors","value_json":"0"},{"key":"gpu.0.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.1.gpu","value_json":"0.0"},{"key":"gpu.1.memory","value_json":"0"},{"key":"gpu.1.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.1.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.1.temp","value_json":"27.0"},{"key":"gpu.1.powerWatts","value_json":"71.537"},{"key":"gpu.1.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.1.powerPercent","value_json":"10.21957142857143"},{"key":"gpu.1.smClock","value_json":"345"},{"key":"gpu.1.memoryClock","value_json":"2619"},{"key":"gpu.1.correctedMemoryErrors","value_json":"0"},{"key":"gpu.1.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.2.gpu","value_json":"0.0"},{"key":"gpu.2.memory","value_json":"0"},{"key":"gpu.2.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.2.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.2.temp","value_json":"28.0"},{"key":"gpu.2.powerWatts","value_json":"71.352"},{"key":"gpu.2.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.2.powerPercent","value_json":"10.193142857142858"},{"key":"gpu.2.smClock","value_json":"345"},{"key":"gpu.2.memoryClock","value_json":"2619"},{"key":"gpu.2.correctedMemoryErrors","value_json":"0"},{"key":"gpu.2.uncorrectedMemoryErrors","value_json":"0"},{"key":"gpu.3.gpu","value_json":"0.0"},{"key":"gpu.3.memory","value_json":"0"},{"key":"gpu.3.memoryAllocated","value_json":"0.695738667712944"},{"key":"gpu.3.memoryAllocatedBytes","value_json":"595001344"},{"key":"gpu.3.temp","value_json":"33.0"},{"key":"gpu.3.powerWatts","value_json":"71.666"},{"key":"gpu.3.enforcedPowerLimitWatts","value_json":"700.0"},{"key":"gpu.3.powerPercent","value_json":"10.238"},{"key":"gpu.3.smClock","value_json":"345"},{"key":"gpu.3.memoryClock","value_json":"2619"},{"key":"gpu.3.correctedMemoryErrors","value_json":"0"},{"key":"gpu.3.uncorrectedMemoryErrors","value_json":"0"}]}}}
{"time":"2025-08-12T18:15:05.561940248-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Stats); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:15:05.561957699-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755047705,"nanos":561920185},"item":[{"key":"cpu","value_json":"1.0295345754595393"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874147340958204"},{"key":"disk./.usageGB","value_json":"17.445926666259766"},{"key":"network.sent","value_json":"8.125717e+06"},{"key":"network.recv","value_json":"1.046425748e+09"},{"key":"proc.memory.percent","value_json":"0.5691172010344429"},{"key":"memory_percent","value_json":"2.442954208723558"},{"key":"proc.memory.availableMB","value_json":"2.002426375e+06"},{"key":"proc.memory.rssMB","value_json":"11745.98828125"}]}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.561976828-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Stats":{"timestamp":{"seconds":1755047705,"nanos":561920185},"item":[{"key":"cpu","value_json":"1.0295345754595393"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874147340958204"},{"key":"disk./.usageGB","value_json":"17.445926666259766"},{"key":"network.sent","value_json":"8.125717e+06"},{"key":"network.recv","value_json":"1.046425748e+09"},{"key":"proc.memory.percent","value_json":"0.5691172010344429"},{"key":"memory_percent","value_json":"2.442954208723558"},{"key":"proc.memory.availableMB","value_json":"2.002426375e+06"},{"key":"proc.memory.rssMB","value_json":"11745.98828125"}]}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.561993151-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":18,"RecordType":{"Stats":{"timestamp":{"seconds":1755047705,"nanos":561920185},"item":[{"key":"cpu","value_json":"1.0295345754595393"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874147340958204"},{"key":"disk./.usageGB","value_json":"17.445926666259766"},{"key":"network.sent","value_json":"8.125717e+06"},{"key":"network.recv","value_json":"1.046425748e+09"},{"key":"proc.memory.percent","value_json":"0.5691172010344429"},{"key":"memory_percent","value_json":"2.442954208723558"},{"key":"proc.memory.availableMB","value_json":"2.002426375e+06"},{"key":"proc.memory.rssMB","value_json":"11745.98828125"}]}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.561999656-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"StartTime":"2025-08-13T01:14:50.083482Z","Record":{"timestamp":{"seconds":1755047705,"nanos":561920185},"item":[{"key":"cpu","value_json":"1.0295345754595393"},{"key":"proc.cpu.threads","value_json":"15"},{"key":"disk./.usagePercent","value_json":"1.9874147340958204"},{"key":"disk./.usageGB","value_json":"17.445926666259766"},{"key":"network.sent","value_json":"8.125717e+06"},{"key":"network.recv","value_json":"1.046425748e+09"},{"key":"proc.memory.percent","value_json":"0.5691172010344429"},{"key":"memory_percent","value_json":"2.442954208723558"},{"key":"proc.memory.availableMB","value_json":"2.002426375e+06"},{"key":"proc.memory.rssMB","value_json":"11745.98828125"}]}}}
{"time":"2025-08-12T18:15:05.680450942-07:00","level":"DEBUG","msg":"handling record","record":{"Request":{"RequestType":{"StopStatus":{}}}}}
{"time":"2025-08-12T18:15:05.68046701-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Request_StopStatus); Control(local:true mailbox_slot:\"2le8txm5mu1a\" connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:05.680473783-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"2le8txm5mu1a","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.68048031-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"2le8txm5mu1a","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.680484395-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"RecordType":{"Request":{"RequestType":{"StopStatus":{}}}},"control":{"local":true,"mailbox_slot":"2le8txm5mu1a","connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.680517723-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:15:05.857500673-07:00","level":"DEBUG","msg":"dispatch: got result","result":{"ResultType":{"Response":{"ResponseType":{"StopStatusResponse":{}}}},"control":{"local":true,"mailbox_slot":"2le8txm5mu1a","connection_id":"127.0.0.1:60224"}}}
{"time":"2025-08-12T18:15:05.863027571-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":862888000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}}}
{"time":"2025-08-12T18:15:05.863050766-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:05.863059911-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":862888000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.863068848-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":862888000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.863076871-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":19,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":862888000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.866292725-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:05.876991036-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:05.898201197-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":0,\"content\":[\"{\\\"ts\\\":\\\"2025-08-13T01:14:51.055733\\\",\\\"content\\\":\\\"Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/mc1xht3q/config.yaml\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:14:51.089775\\\",\\\"content\\\":\\\"Restoring states from the checkpoint path at /common/lidxxlab/cmrchallenge/code/chaowei/experiments/cmr25/promptmr-plus/CMR2025/deep_recon/uec2kxvx/checkpoints/last.ckpt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:14:51.089811\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/fabric/utilities/cloud_io.py:57: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:05.863082\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/utilities/migration/utils.py:56: The loaded checkpoint was produced with Lightning v2.5.1, which is newer than your current Lightning version: v2.3.3\\\"}\"]},\"wandb-events.jsonl\":{\"offset\":0,\"content\":[\"{\\\"system.gpu.1.gpu\\\":0,\\\"system.gpu.1.memoryClock\\\":2619,\\\"system.gpu.2.gpu\\\":0,\\\"system.gpu.2.memory\\\":0,\\\"system.gpu.0.correctedMemoryErrors\\\":0,\\\"system.gpu.1.temp\\\":27,\\\"system.gpu.1.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.2.memoryClock\\\":2619,\\\"system.gpu.3.memoryClock\\\":2619,\\\"system.gpu.1.correctedMemoryErrors\\\":0,\\\"_wandb\\\":true,\\\"_runtime\\\":15.46112963,\\\"system.gpu.0.memory\\\":0,\\\"system.gpu.0.memoryAllocatedBytes\\\":1569718272,\\\"system.gpu.0.temp\\\":34,\\\"system.gpu.0.powerWatts\\\":111.902,\\\"system.gpu.0.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.2.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.2.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.2.smClock\\\":345,\\\"system.gpu.3.correctedMemoryErrors\\\":0,\\\"system.gpu.3.memoryAllocatedBytes\\\":595001344,\\\"_timestamp\\\":1.7550477055446117e+09,\\\"system.gpu.0.memoryClock\\\":2619,\\\"system.gpu.1.memory\\\":0,\\\"system.gpu.2.temp\\\":28,\\\"system.gpu.2.powerPercent\\\":10.193142857142858,\\\"system.gpu.2.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.3.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.3.powerPercent\\\":10.238,\\\"system.gpu.0.memoryAllocated\\\":1.8354810627889013,\\\"system.gpu.1.smClock\\\":345,\\\"system.gpu.1.memoryAllocatedBytes\\\":595001344,\\\"system.gpu.1.powerPercent\\\":10.21957142857143,\\\"system.gpu.1.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.2.powerWatts\\\":71.352,\\\"system.gpu.3.gpu\\\":0,\\\"system.gpu.3.powerWatts\\\":71.666,\\\"system.gpu.3.uncorrectedMemoryErrors\\\":0,\\\"system.gpu.0.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.0.powerPercent\\\":15.986,\\\"system.gpu.0.smClock\\\":1980,\\\"system.gpu.1.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.2.memoryAllocated\\\":0.695738667712944,\\\"system.gpu.3.memory\\\":0,\\\"system.gpu.0.gpu\\\":0,\\\"system.gpu.1.powerWatts\\\":71.537,\\\"system.gpu.2.correctedMemoryErrors\\\":0,\\\"system.gpu.3.temp\\\":33,\\\"system.gpu.3.enforcedPowerLimitWatts\\\":700,\\\"system.gpu.3.smClock\\\":345}\",\"{\\\"system.proc.memory.availableMB\\\":2.002426375e+06,\\\"_runtime\\\":15.478438185,\\\"system.cpu\\\":1.0295345754595393,\\\"system.proc.cpu.threads\\\":15,\\\"system.disk./.usagePercent\\\":1.9874147340958204,\\\"system.network.recv\\\":1.046425748e+09,\\\"system.memory_percent\\\":2.442954208723558,\\\"_wandb\\\":true,\\\"_timestamp\\\":1.7550477055619202e+09,\\\"system.disk./.usageGB\\\":17.445926666259766,\\\"system.network.sent\\\":8.125717e+06,\\\"system.proc.memory.percent\\\":0.5691172010344429,\\\"system.proc.memory.rssMB\\\":11745.98828125}\"]}},\"uploaded\":[\"requirements.txt\"]}"}
{"time":"2025-08-12T18:15:05.898264593-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/mc1xht3q/file_stream"}
{"time":"2025-08-12T18:15:05.963713808-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963477000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}}}
{"time":"2025-08-12T18:15:05.96373436-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:05.963741203-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963477000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.963752845-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963477000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.963758708-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":20,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963477000},"line":"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\"].\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.96398424-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963732000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}}}
{"time":"2025-08-12T18:15:05.963991648-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:05.963995618-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963732000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.964000572-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963732000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.964004954-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":21,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047705,"nanos":963732000},"line":"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:05.967088903-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:05.976851295-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:06.042355395-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":null,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:15:06.187825636-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":187601000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}}}
{"time":"2025-08-12T18:15:06.187842426-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.187848579-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":187601000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.187855669-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":187601000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.187861282-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":22,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":187601000},"line":"\n  | Name             | Type                 | Params | Mode \n------------------------------------------------------------------\n0 | NMSE             | DistributedMetricSum | 0      | train\n1 | SSIM             | DistributedMetricSum | 0      | train\n2 | PSNR             | DistributedMetricSum | 0      | train\n3 | ValLoss          | DistributedMetricSum | 0      | train\n4 | TotExamples      | DistributedMetricSum | 0      | train\n5 | TotSliceExamples | DistributedMetricSum | 0      | train\n6 | promptmr         | PromptMR             | 82.3 M | train\n7 | loss             | SSIMLoss             | 0      | train\n------------------------------------------------------------------\n72.0 M    Trainable params\n10.4 M    Non-trainable params\n82.3 M    Total params\n329.273   Total estimated model params size (MB)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.188746134-07:00","level":"DEBUG","msg":"handling record","record":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}}}
{"time":"2025-08-12T18:15:06.188764161-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Config); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.188773491-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.188798828-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.188820481-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":23,"RecordType":{"Config":{"update":[{"key":"class_path","value_json":"\"pl_modules.PromptMrModule\""},{"key":"init_args","value_json":"{\"num_cascades\": 12, \"num_adj_slices\": 5, \"n_feat0\": 48, \"feature_dim\": [72, 96, 120], \"prompt_dim\": [24, 48, 72], \"sens_n_feat0\": 24, \"sens_feature_dim\": [36, 48, 60], \"sens_prompt_dim\": [12, 24, 36], \"len_prompt\": [5, 5, 5], \"prompt_size\": [64, 32, 16], \"n_enc_cab\": [2, 3, 3], \"n_dec_cab\": [2, 2, 3], \"n_skip_cab\": [1, 1, 1], \"n_bottleneck_cab\": 3, \"no_use_ca\": false, \"learnable_prompt\": false, \"adaptive_input\": true, \"n_buffer\": 4, \"n_history\": 0, \"use_sens_adj\": true, \"model_version\": \"promptmr_v2\", \"lr\": 0.0002, \"lr_step_size\": 11, \"lr_gamma\": 0.1, \"weight_decay\": 0.01, \"use_checkpoint\": false, \"compute_sens_per_coil\": false, \"pretrain\": false, \"num_log_images\": 16}"},{"key":"_instantiator","value_json":"\"lightning.pytorch.cli.instantiate_module\""}]}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.191089161-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:06.374913349-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374707000},"line":"Traceback (most recent call last):\n"}}}
{"time":"2025-08-12T18:15:06.374927068-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.374938212-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374777000}}}}
{"time":"2025-08-12T18:15:06.374942342-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.37494958-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374825000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}}}
{"time":"2025-08-12T18:15:06.374952995-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:15:06.374959908-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374843000}}}}
{"time":"2025-08-12T18:15:06.374962937-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:15:06.374974421-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374859000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}}}
{"time":"2025-08-12T18:15:06.374984942-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":3}
{"time":"2025-08-12T18:15:06.374991562-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374873000}}}}
{"time":"2025-08-12T18:15:06.374994747-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":4}
{"time":"2025-08-12T18:15:06.374998098-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374707000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375003071-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374777000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375007387-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374825000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375010074-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374843000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375012923-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374859000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37501547-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374873000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375018634-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374707000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375021753-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374777000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375024209-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374825000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375026478-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374843000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375028623-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374859000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375031017-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374873000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375034638-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":24,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374707000},"line":"Traceback (most recent call last):\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375047667-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":25,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374777000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375052448-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":26,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374825000},"line":"  File \"main.py\", line 244, in <module>\n    run_cli()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375063527-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":27,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374843000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375066444-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":28,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374859000},"line":"  File \"main.py\", line 236, in run_cli\n    cli = CustomLightningCLI(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375086712-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":29,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374873000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375102314-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374886000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}}}
{"time":"2025-08-12T18:15:06.375106466-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375113558-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374901000}}}}
{"time":"2025-08-12T18:15:06.375117069-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375123582-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374914000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}}}
{"time":"2025-08-12T18:15:06.375126817-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:15:06.375150091-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374927000}}}}
{"time":"2025-08-12T18:15:06.375153472-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:15:06.375160325-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374940000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}}}
{"time":"2025-08-12T18:15:06.375163751-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":3}
{"time":"2025-08-12T18:15:06.375171361-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374952000}}}}
{"time":"2025-08-12T18:15:06.375174597-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":4}
{"time":"2025-08-12T18:15:06.375185184-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374964000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}}}
{"time":"2025-08-12T18:15:06.375188746-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":5}
{"time":"2025-08-12T18:15:06.375176478-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374886000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375201094-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374901000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375205436-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374914000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375208839-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374927000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375211613-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374940000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375214597-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374952000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375217013-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374964000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375222983-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374886000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375226903-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374901000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375238213-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374914000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375241525-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374927000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37524379-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374940000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375246516-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374952000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375248841-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374964000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37519477-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374977000}}}}
{"time":"2025-08-12T18:15:06.375265614-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375279891-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374990000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:15:06.375284867-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.37529181-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375002000}}}}
{"time":"2025-08-12T18:15:06.375295986-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:15:06.375302977-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375014000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}}}
{"time":"2025-08-12T18:15:06.375306761-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:15:06.375312722-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375024000}}}}
{"time":"2025-08-12T18:15:06.375318605-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":3}
{"time":"2025-08-12T18:15:06.375324831-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375040000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}}}
{"time":"2025-08-12T18:15:06.375328383-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":4}
{"time":"2025-08-12T18:15:06.375274114-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":30,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374886000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n    self._run_subcommand(self.subcommand)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375336132-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375051000}}}}
{"time":"2025-08-12T18:15:06.375340111-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":5}
{"time":"2025-08-12T18:15:06.375346613-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375061000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}}}
{"time":"2025-08-12T18:15:06.375350815-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":6}
{"time":"2025-08-12T18:15:06.37535694-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375074000}}}}
{"time":"2025-08-12T18:15:06.375345298-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374977000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375370094-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374990000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375375737-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":31,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374901000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375378904-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375002000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375381638-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":32,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374914000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n    fn(**fn_kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375388184-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374977000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37539498-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374990000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375397904-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375002000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375360759-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":7}
{"time":"2025-08-12T18:15:06.375418395-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":33,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374927000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375382878-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375014000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375423797-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":34,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374940000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n    call._call_and_handle_interrupt(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375426838-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375024000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375427399-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375085000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}}}
{"time":"2025-08-12T18:15:06.375431011-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375040000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375434378-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375051000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375438588-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375061000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375441347-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375074000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375442661-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375444862-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375014000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375450307-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375024000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375452976-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375040000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375454516-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375097000}}}}
{"time":"2025-08-12T18:15:06.375461244-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375468725-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375085000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375473833-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375097000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375470929-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375111000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}}}
{"time":"2025-08-12T18:15:06.375480398-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":35,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374952000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375483817-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375484781-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":36,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374964000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375492061-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375123000}}}}
{"time":"2025-08-12T18:15:06.375455742-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375051000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375498153-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375111000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375495985-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375515142-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}}}
{"time":"2025-08-12T18:15:06.375519802-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375526783-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375144000}}}}
{"time":"2025-08-12T18:15:06.375530999-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:15:06.375499477-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375061000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375537897-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375154000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:15:06.375538968-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":37,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374977000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375544671-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":38,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":374990000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n    return function(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375544666-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375123000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375549867-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375553548-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375144000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375539187-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375074000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375569734-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375085000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375573312-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375097000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375576088-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375111000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375579153-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375123000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375580887-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":39,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375002000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375586789-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":40,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375014000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n    self._run(model, ckpt_path=ckpt_path)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375542288-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:15:06.375603323-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375164000}}}}
{"time":"2025-08-12T18:15:06.375608228-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375581893-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375622095-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375174000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}}}
{"time":"2025-08-12T18:15:06.375625628-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375144000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37562713-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:15:06.375629641-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":41,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375024000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375628958-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375154000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37563412-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":42,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375040000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n    self._checkpoint_connector.restore_training_state()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375635719-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375164000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375638089-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375185000}}}}
{"time":"2025-08-12T18:15:06.375642156-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375154000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375647265-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375164000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375643898-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375660177-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375196000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}}}
{"time":"2025-08-12T18:15:06.375664629-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":1}
{"time":"2025-08-12T18:15:06.37567082-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375207000}}}}
{"time":"2025-08-12T18:15:06.375674846-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:15:06.375639202-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375174000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375684555-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375224000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}}}
{"time":"2025-08-12T18:15:06.375684577-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375185000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375672773-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":43,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375051000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375691407-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375196000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37569507-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375207000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375694845-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":44,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375061000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n    self.restore_optimizers_and_schedulers()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375700052-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375174000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375704844-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375185000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375707384-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375196000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.37571105-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375207000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375689292-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":2}
{"time":"2025-08-12T18:15:06.375723739-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375224000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375725386-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375239000}}}}
{"time":"2025-08-12T18:15:06.375727935-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375224000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375730352-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.375734114-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375239000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375738237-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375239000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.3757504-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":45,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375074000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375757501-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":46,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375085000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n    self.restore_optimizers()\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375795247-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":47,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375097000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375797971-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":48,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375111000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375833676-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":49,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375123000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375836427-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":50,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375133000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n    optimizer.load_state_dict(opt_state)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375867763-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":51,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375144000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375870318-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":52,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375154000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n    return disable_fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375891885-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":53,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375164000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375895166-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":54,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375174000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n    return fn(*args, **kwargs)\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375921054-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":55,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375185000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375925241-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":56,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375196000},"line":"  File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n    raise ValueError(\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375947841-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":57,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375207000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375950586-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":58,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375224000},"line":"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.375965383-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":59,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":375239000}}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.377803928-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:06.378825226-07:00","level":"DEBUG","msg":"handling record","record":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":378704000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}}}
{"time":"2025-08-12T18:15:06.378841507-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_OutputRaw); Control(connection_id:\"127.0.0.1:60224\")","buffer":0}
{"time":"2025-08-12T18:15:06.378850172-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":378704000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.378863592-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":378704000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.378877809-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":60,"RecordType":{"OutputRaw":{"timestamp":{"seconds":1755047706,"nanos":378704000},"line":"[rank0]: Traceback (most recent call last):\n[rank0]:   File \"main.py\", line 244, in <module>\n[rank0]:     run_cli()\n[rank0]:   File \"main.py\", line 236, in run_cli\n[rank0]:     cli = CustomLightningCLI(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 394, in __init__\n[rank0]:     self._run_subcommand(self.subcommand)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\", line 701, in _run_subcommand\n[rank0]:     fn(**fn_kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 543, in fit\n[rank0]:     call._call_and_handle_interrupt(\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\", line 43, in _call_and_handle_interrupt\n[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\", line 105, in launch\n[rank0]:     return function(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 579, in _fit_impl\n[rank0]:     self._run(model, ckpt_path=ckpt_path)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\", line 977, in _run\n[rank0]:     self._checkpoint_connector.restore_training_state()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 298, in restore_training_state\n[rank0]:     self.restore_optimizers_and_schedulers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 368, in restore_optimizers_and_schedulers\n[rank0]:     self.restore_optimizers()\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\", line 383, in restore_optimizers\n[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\", line 376, in load_optimizer_state_dict\n[rank0]:     optimizer.load_state_dict(opt_state)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\", line 31, in inner\n[rank0]:     return disable_fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\", line 600, in _fn\n[rank0]:     return fn(*args, **kwargs)\n[rank0]:   File \"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\", line 854, in load_state_dict\n[rank0]:     raise ValueError(\n[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\n"}},"control":{"connection_id":"127.0.0.1:60224"},"_info":{"stream_id":"mc1xht3q"}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.389586573-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Lines":{}}}
{"time":"2025-08-12T18:15:06.859407323-07:00","level":"DEBUG","msg":"handling record","record":{"Exit":{"exit_code":1}}}
{"time":"2025-08-12T18:15:06.859458305-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkRecord(*service_go_proto.Record_Exit); Control(always_send:true)","buffer":0}
{"time":"2025-08-12T18:15:06.859463535-07:00","level":"INFO","msg":"stream: closing","id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859470239-07:00","level":"DEBUG","msg":"handler: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859481127-07:00","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-08-12T18:15:06.859486419-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"16"}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859496743-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":61,"RecordType":{"Summary":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"16"}]}}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859503046-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:15:06.859508249-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"Record":{"update":[{"nested_key":["_wandb","runtime"],"value_json":"16"}]}}}
{"time":"2025-08-12T18:15:06.859519369-07:00","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-08-12T18:15:06.859534195-07:00","level":"DEBUG","msg":"write: Do: got work","work":{"Record":{"RecordType":{"Exit":{"exit_code":1,"runtime":16}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859546979-07:00","level":"DEBUG","msg":"sender: got work","work":{"Record":{"num":62,"RecordType":{"Exit":{"exit_code":1,"runtime":16}},"control":{"always_send":true}}},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859574129-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(1))","buffer":0}
{"time":"2025-08-12T18:15:06.859582186-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859593541-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859598347-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859608034-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(2))","buffer":0}
{"time":"2025-08-12T18:15:06.859610691-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859612902-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.859618821-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.866727038-07:00","level":"DEBUG","msg":"Flushing debouncer"}
{"time":"2025-08-12T18:15:06.866814921-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:15:06.909859594-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["output.log","wandb-summary.json"]}
{"time":"2025-08-12T18:15:06.909936336-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/output.log, Name: output.log, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011506Z&X-Goog-Expires=86399&X-Goog-Signature=b19491f05dce437759cd20aabeda8d7f82d7a797dbb2f73bd73d1891b0f052e36bf61356b8a68caea7bbf3acaa831744ff6458598a8866b1e088a8093289f73583f357bf3d1741d8b8bc448c1fe282f8422c08a96904c17e89b23d4087d7f845a07257e0ed3fc7ddd7b7db7459eb69630f917c09857b14f9f6943b28e44d04851ff8af1396fac2c454e9e99c1eb0787c820a2a2f877ea512229a2ee135abf3f050ac60d38f7d8a49906ce331b5b4a31f38e174fc34a5ee339eceff3bd6360e53962824bcb120a7f92ba29f68e45e59de83b05150afffb3a02fac6d06cb9281b6c014976f615a63a97fe9c9155012777a6fb03f9f075d0517e017f50dd9649f51&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/wandb-summary.json, Name: wandb-summary.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011506Z&X-Goog-Expires=86399&X-Goog-Signature=88c13910f8803703fdbc01094084ad8fba8e1ed24b633facd7f8aac03bd3805b9793cd2152116a9de600e102faa333f66eee668a98654a2a464e2a51a725c57db244116715ed532ecdbd7dac12d560ab204143724e74f9720ff881936835bb8c8735a01c7ff18b6cc9b3b22b622a7c4de43fd9ebfb42144e89dcc1d9e057a8ec1b4d41667389816861d63c4cab9ca773dae8c235e7305c4f79b984b3582eec34a451cfe2096a39c96a6997df166931e0e6f9a2d75f3db0a78403fb3cbbe66d9a48013f0de504d1896b7b42d7f69bff9230a3414a85130261982b03538539a224b66c2a954502e3dedd9ab9b89a2bbbd28e21bc0ce02c7e5bab37e7d8ea3a906f&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/wandb-summary.json","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011506Z&X-Goog-Expires=86399&X-Goog-Signature=88c13910f8803703fdbc01094084ad8fba8e1ed24b633facd7f8aac03bd3805b9793cd2152116a9de600e102faa333f66eee668a98654a2a464e2a51a725c57db244116715ed532ecdbd7dac12d560ab204143724e74f9720ff881936835bb8c8735a01c7ff18b6cc9b3b22b622a7c4de43fd9ebfb42144e89dcc1d9e057a8ec1b4d41667389816861d63c4cab9ca773dae8c235e7305c4f79b984b3582eec34a451cfe2096a39c96a6997df166931e0e6f9a2d75f3db0a78403fb3cbbe66d9a48013f0de504d1896b7b42d7f69bff9230a3414a85130261982b03538539a224b66c2a954502e3dedd9ab9b89a2bbbd28e21bc0ce02c7e5bab37e7d8ea3a906f&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/output.log","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011506Z&X-Goog-Expires=86399&X-Goog-Signature=b19491f05dce437759cd20aabeda8d7f82d7a797dbb2f73bd73d1891b0f052e36bf61356b8a68caea7bbf3acaa831744ff6458598a8866b1e088a8093289f73583f357bf3d1741d8b8bc448c1fe282f8422c08a96904c17e89b23d4087d7f845a07257e0ed3fc7ddd7b7db7459eb69630f917c09857b14f9f6943b28e44d04851ff8af1396fac2c454e9e99c1eb0787c820a2a2f877ea512229a2ee135abf3f050ac60d38f7d8a49906ce331b5b4a31f38e174fc34a5ee339eceff3bd6360e53962824bcb120a7f92ba29f68e45e59de83b05150afffb3a02fac6d06cb9281b6c014976f615a63a97fe9c9155012777a6fb03f9f075d0517e017f50dd9649f51&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/output.log?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011506Z&X-Goog-Expires=86399&X-Goog-Signature=b19491f05dce437759cd20aabeda8d7f82d7a797dbb2f73bd73d1891b0f052e36bf61356b8a68caea7bbf3acaa831744ff6458598a8866b1e088a8093289f73583f357bf3d1741d8b8bc448c1fe282f8422c08a96904c17e89b23d4087d7f845a07257e0ed3fc7ddd7b7db7459eb69630f917c09857b14f9f6943b28e44d04851ff8af1396fac2c454e9e99c1eb0787c820a2a2f877ea512229a2ee135abf3f050ac60d38f7d8a49906ce331b5b4a31f38e174fc34a5ee339eceff3bd6360e53962824bcb120a7f92ba29f68e45e59de83b05150afffb3a02fac6d06cb9281b6c014976f615a63a97fe9c9155012777a6fb03f9f075d0517e017f50dd9649f51&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/wandb-summary.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011506Z&X-Goog-Expires=86399&X-Goog-Signature=88c13910f8803703fdbc01094084ad8fba8e1ed24b633facd7f8aac03bd3805b9793cd2152116a9de600e102faa333f66eee668a98654a2a464e2a51a725c57db244116715ed532ecdbd7dac12d560ab204143724e74f9720ff881936835bb8c8735a01c7ff18b6cc9b3b22b622a7c4de43fd9ebfb42144e89dcc1d9e057a8ec1b4d41667389816861d63c4cab9ca773dae8c235e7305c4f79b984b3582eec34a451cfe2096a39c96a6997df166931e0e6f9a2d75f3db0a78403fb3cbbe66d9a48013f0de504d1896b7b42d7f69bff9230a3414a85130261982b03538539a224b66c2a954502e3dedd9ab9b89a2bbbd28e21bc0ce02c7e5bab37e7d8ea3a906f&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:15:06.********-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(3))","buffer":0}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.*********-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.997533604-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.997537839-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(4))","buffer":0}
{"time":"2025-08-12T18:15:06.997540236-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.997542457-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:06.997544985-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.047849286-07:00","level":"DEBUG","msg":"runfiles: uploading files","files":["config.yaml"]}
{"time":"2025-08-12T18:15:07.047914469-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/graphql"}
{"time":"2025-08-12T18:15:07.*********-07:00","level":"DEBUG","msg":"fileTransferManager: AddTask: adding task","task":"DefaultUploadTask{FileKind: 1, Path: /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/config.yaml, Name: config.yaml, Url: https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011507Z&X-Goog-Expires=86399&X-Goog-Signature=79f5f46d222f18bc83e968d4af99428ea20f5551156f63b22b38c952637b436c5042466c4103369d792996798502cffc340935b3fc405ee1db90bd6da83985743de98724c5808ae865cd5ed808261c866a2b045f6e82f1df4b5cc09eb71e6669f75e43f678ff8f91aa1f8331cb34b7dac2f3d3aa6c468f47f7a59254c7b6ffa940074bc0a0686a8ae0620d628a82349f296ba3a2899f43c1a7b73b13f4efa3bb2bee6373588166e6a566beeda8d8d42da2e71381980a6f9a0024610a86cad113401f3d16a6e9a8f1f4aaac9b0addf087f12d94add0f40dff50d2111508c14abe0b5458e696b2e734d87aafaed522e4911346841ca81083bd0fa5a9722e8929fe&X-Goog-SignedHeaders=host&X-User=lisha-zeng, Size: 0}"}
{"time":"2025-08-12T18:15:07.*********-07:00","level":"DEBUG","msg":"default file transfer: uploading file","path":"/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/wandb/run-20250812_181450-mc1xht3q/files/config.yaml","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011507Z&X-Goog-Expires=86399&X-Goog-Signature=79f5f46d222f18bc83e968d4af99428ea20f5551156f63b22b38c952637b436c5042466c4103369d792996798502cffc340935b3fc405ee1db90bd6da83985743de98724c5808ae865cd5ed808261c866a2b045f6e82f1df4b5cc09eb71e6669f75e43f678ff8f91aa1f8331cb34b7dac2f3d3aa6c468f47f7a59254c7b6ffa940074bc0a0686a8ae0620d628a82349f296ba3a2899f43c1a7b73b13f4efa3bb2bee6373588166e6a566beeda8d8d42da2e71381980a6f9a0024610a86cad113401f3d16a6e9a8f1f4aaac9b0addf087f12d94add0f40dff50d2111508c14abe0b5458e696b2e734d87aafaed522e4911346841ca81083bd0fa5a9722e8929fe&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:15:07.*********-07:00","level":"DEBUG","msg":"performing request","method":"PUT","url":"https://storage.googleapis.com/wandb-production.appspot.com/lisha-zeng-cedars-sinai/task3/mc1xht3q/config.yaml?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250813%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250813T011507Z&X-Goog-Expires=86399&X-Goog-Signature=79f5f46d222f18bc83e968d4af99428ea20f5551156f63b22b38c952637b436c5042466c4103369d792996798502cffc340935b3fc405ee1db90bd6da83985743de98724c5808ae865cd5ed808261c866a2b045f6e82f1df4b5cc09eb71e6669f75e43f678ff8f91aa1f8331cb34b7dac2f3d3aa6c468f47f7a59254c7b6ffa940074bc0a0686a8ae0620d628a82349f296ba3a2899f43c1a7b73b13f4efa3bb2bee6373588166e6a566beeda8d8d42da2e71381980a6f9a0024610a86cad113401f3d16a6e9a8f1f4aaac9b0addf087f12d94add0f40dff50d2111508c14abe0b5458e696b2e734d87aafaed522e4911346841ca81083bd0fa5a9722e8929fe&X-Goog-SignedHeaders=host&X-User=lisha-zeng"}
{"time":"2025-08-12T18:15:07.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"output.log"}}
{"time":"2025-08-12T18:15:07.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"wandb-summary.json"}}
{"time":"2025-08-12T18:15:07.*********-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"RelativePath":"config.yaml"}}
{"time":"2025-08-12T18:15:07.299868048-07:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-12T18:15:07.299874455-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(5))","buffer":0}
{"time":"2025-08-12T18:15:07.299880038-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.299884397-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.2998879-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.299891236-07:00","level":"DEBUG","msg":"filestream: stream update","update":{"ExitCode":1}}
{"time":"2025-08-12T18:15:07.299983526-07:00","level":"DEBUG","msg":"filestream: post request","request":"{\"files\":{\"output.log\":{\"offset\":4,\"content\":[\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:05.963763\\\",\\\"content\\\":\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py:265: Be aware that when using `ckpt_path`, callbacks used to create the checkpoint need to be provided during `Trainer` instantiation. Please add the following callbacks: [\\\\\\\"ModelCheckpoint{'monitor': 'validation_loss', 'mode': 'min', 'every_n_train_steps': 0, 'every_n_epochs': 1, 'train_time_interval': None}\\\\\\\"].\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:05.964008\\\",\\\"content\\\":\\\"LOCAL_RANK: 0 - CUDA_VISIBLE_DEVICES: [0,1,2,3]\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187867\\\",\\\"content\\\":\\\"\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187869\\\",\\\"content\\\":\\\"  | Name             | Type                 | Params | Mode\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187875\\\",\\\"content\\\":\\\"------------------------------------------------------------------\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187888\\\",\\\"content\\\":\\\"0 | NMSE             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187894\\\",\\\"content\\\":\\\"1 | SSIM             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187901\\\",\\\"content\\\":\\\"2 | PSNR             | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187906\\\",\\\"content\\\":\\\"3 | ValLoss          | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187911\\\",\\\"content\\\":\\\"4 | TotExamples      | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187917\\\",\\\"content\\\":\\\"5 | TotSliceExamples | DistributedMetricSum | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187928\\\",\\\"content\\\":\\\"6 | promptmr         | PromptMR             | 82.3 M | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187933\\\",\\\"content\\\":\\\"7 | loss             | SSIMLoss             | 0      | train\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187936\\\",\\\"content\\\":\\\"------------------------------------------------------------------\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187950\\\",\\\"content\\\":\\\"72.0 M    Trainable params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187954\\\",\\\"content\\\":\\\"10.4 M    Non-trainable params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187961\\\",\\\"content\\\":\\\"82.3 M    Total params\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.187963\\\",\\\"content\\\":\\\"329.273   Total estimated model params size (MB)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375038\\\",\\\"content\\\":\\\"Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375055\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375061\\\",\\\"content\\\":\\\"    run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375068\\\",\\\"content\\\":\\\"  File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375082\\\",\\\"content\\\":\\\"    cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375334\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375367\\\",\\\"content\\\":\\\"    self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375389\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375415\\\",\\\"content\\\":\\\"    fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375427\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375474\\\",\\\"content\\\":\\\"    call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375487\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375519\\\",\\\"content\\\":\\\"    return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375547\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375575\\\",\\\"content\\\":\\\"    return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375589\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375620\\\",\\\"content\\\":\\\"    self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375637\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 977, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375660\\\",\\\"content\\\":\\\"    self._checkpoint_connector.restore_training_state()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375702\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 298, in restore_training_state\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375743\\\",\\\"content\\\":\\\"    self.restore_optimizers_and_schedulers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375760\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 368, in restore_optimizers_and_schedulers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375790\\\",\\\"content\\\":\\\"    self.restore_optimizers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375800\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 383, in restore_optimizers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375825\\\",\\\"content\\\":\\\"    self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375838\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 376, in load_optimizer_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375860\\\",\\\"content\\\":\\\"    optimizer.load_state_dict(opt_state)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375872\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\\\\\\\", line 31, in inner\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375887\\\",\\\"content\\\":\\\"    return disable_fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375897\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\\\\\\\", line 600, in _fn\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375916\\\",\\\"content\\\":\\\"    return fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375927\\\",\\\"content\\\":\\\"  File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 854, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375942\\\",\\\"content\\\":\\\"    raise ValueError(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.375953\\\",\\\"content\\\":\\\"ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378884\\\",\\\"content\\\":\\\"[rank0]: Traceback (most recent call last):\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378894\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 244, in \\\\u003cmodule\\\\u003e\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378901\\\",\\\"content\\\":\\\"[rank0]:     run_cli()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378904\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"main.py\\\\\\\", line 236, in run_cli\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378909\\\",\\\"content\\\":\\\"[rank0]:     cli = CustomLightningCLI(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378914\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 394, in __init__\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378934\\\",\\\"content\\\":\\\"[rank0]:     self._run_subcommand(self.subcommand)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378939\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/cli.py\\\\\\\", line 701, in _run_subcommand\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378961\\\",\\\"content\\\":\\\"[rank0]:     fn(**fn_kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378967\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 543, in fit\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378986\\\",\\\"content\\\":\\\"[rank0]:     call._call_and_handle_interrupt(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.378991\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/call.py\\\\\\\", line 43, in _call_and_handle_interrupt\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379011\\\",\\\"content\\\":\\\"[rank0]:     return trainer.strategy.launcher.launch(trainer_fn, *args, trainer=trainer, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379024\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/launchers/subprocess_script.py\\\\\\\", line 105, in launch\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379045\\\",\\\"content\\\":\\\"[rank0]:     return function(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379051\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 579, in _fit_impl\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379067\\\",\\\"content\\\":\\\"[rank0]:     self._run(model, ckpt_path=ckpt_path)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379073\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/trainer.py\\\\\\\", line 977, in _run\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379086\\\",\\\"content\\\":\\\"[rank0]:     self._checkpoint_connector.restore_training_state()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379092\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 298, in restore_training_state\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379114\\\",\\\"content\\\":\\\"[rank0]:     self.restore_optimizers_and_schedulers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379119\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 368, in restore_optimizers_and_schedulers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379146\\\",\\\"content\\\":\\\"[rank0]:     self.restore_optimizers()\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379152\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/trainer/connectors/checkpoint_connector.py\\\\\\\", line 383, in restore_optimizers\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379174\\\",\\\"content\\\":\\\"[rank0]:     self.trainer.strategy.load_optimizer_state_dict(self._loaded_checkpoint)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379183\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/lightning/pytorch/strategies/strategy.py\\\\\\\", line 376, in load_optimizer_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379203\\\",\\\"content\\\":\\\"[rank0]:     optimizer.load_state_dict(opt_state)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379207\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_compile.py\\\\\\\", line 31, in inner\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379220\\\",\\\"content\\\":\\\"[rank0]:     return disable_fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379225\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/_dynamo/eval_frame.py\\\\\\\", line 600, in _fn\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379237\\\",\\\"content\\\":\\\"[rank0]:     return fn(*args, **kwargs)\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379241\\\",\\\"content\\\":\\\"[rank0]:   File \\\\\\\"/home/<USER>/miniconda3/envs/promptmr/lib/python3.8/site-packages/torch/optim/optimizer.py\\\\\\\", line 854, in load_state_dict\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379256\\\",\\\"content\\\":\\\"[rank0]:     raise ValueError(\\\"}\",\"{\\\"level\\\":\\\"error\\\",\\\"ts\\\":\\\"2025-08-13T01:15:06.379259\\\",\\\"content\\\":\\\"[rank0]: ValueError: loaded state dict contains a parameter group that doesn't match the size of optimizer's group\\\"}\"]},\"wandb-summary.json\":{\"offset\":0,\"content\":[\"{\\\"_wandb\\\":{\\\"runtime\\\":16}}\"]}},\"uploaded\":[\"output.log\",\"wandb-summary.json\",\"config.yaml\"],\"complete\":true,\"exitcode\":1}"}
{"time":"2025-08-12T18:15:07.300051551-07:00","level":"DEBUG","msg":"[DEBUG] POST https://api.wandb.ai/files/lisha-zeng-cedars-sinai/cmr2025_task3/mc1xht3q/file_stream"}
{"time":"2025-08-12T18:15:07.433590416-07:00","level":"DEBUG","msg":"filestream: post response","response":{"exitcode":1,"limits":{"gpu_enabled":null,"hub_settings":{"disk":"10Gi","docker_enabled":false,"expiration":259200,"image":null,"redis_enabled":false,"repo":"lukas/ml-class"},"name":"default","noContact":false,"private_projects":true,"proxy_settings":{"openai":null},"rate_limit":"400/s","restricted":false,"sweeps_enabled":false,"system_metrics":"2/m","teams_enabled":false}}}
{"time":"2025-08-12T18:15:07.433627563-07:00","level":"DEBUG","msg":"filestream: closed"}
{"time":"2025-08-12T18:15:07.433635296-07:00","level":"DEBUG","msg":"runwork: got work","work":"WorkSentinel(senderSentinel(6))","buffer":0}
{"time":"2025-08-12T18:15:07.433644593-07:00","level":"DEBUG","msg":"handler: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.43364952-07:00","level":"DEBUG","msg":"write: Do: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.433654686-07:00","level":"DEBUG","msg":"sender: got work","work":{},"stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.433661372-07:00","level":"INFO","msg":"handler: closed","stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.433664122-07:00","level":"INFO","msg":"writer: Close: closed","stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.43367127-07:00","level":"INFO","msg":"sender: closed","stream_id":"mc1xht3q"}
{"time":"2025-08-12T18:15:07.435826111-07:00","level":"INFO","msg":"stream: closed","id":"mc1xht3q"}
