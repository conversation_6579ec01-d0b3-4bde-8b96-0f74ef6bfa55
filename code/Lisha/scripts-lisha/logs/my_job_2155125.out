CUDA_VISIBLE_DEVICES = 0,1,2,3
index, name, memory.total [MiB]
0, NVIDIA H100 80GB HBM3, 81559 MiB
1, NVIDIA H100 80GB HBM3, 81559 MiB
2, NVIDIA H100 80GB HBM3, 81559 MiB
3, NVIDIA H100 80GB HBM3, 81559 MiB
visible: 0,1,2,3
CUDA available: True
Num GPUs: 4
  [0] NVIDIA H100 80GB HBM3  •  84.9 GB
  [1] NVIDIA H100 80GB HBM3  •  84.9 GB
  [2] NVIDIA H100 80GB HBM3  •  84.9 GB
  [3] NVIDIA H100 80GB HBM3  •  84.9 GB
Python path: /home/<USER>/miniconda3/envs/promptmr/bin/python
CUDA version (in PyTorch): 12.1
Wandb status: Current Settings
{
  "_extra_http_headers": null,
  "_proxies": null,
  "api_key": null,
  "base_url": "https://api.wandb.ai",
  "entity": null,
  "git_remote": "origin",
  "ignore_globs": [],
  "organization": null,
  "project": null,
  "root_dir": null,
  "section": "default"
}
Running on node: esplhpc-cp088, SLURM_PROCID=0
loading pretrain weights from /common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt
Configuration saved to /common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments/cmr2025_task3/0wpkypoo/config.yaml

Sanity Checking: |          | 0/? [00:00<?, ?it/s]
Sanity Checking:   0%|          | 0/2 [00:00<?, ?it/s]
Sanity Checking DataLoader 0:   0%|          | 0/2 [00:00<?, ?it/s]mask: torch.Size([1, 516, 161])
output shape: torch.Size([1, 516, 161])

Sanity Checking DataLoader 0:  50%|█████     | 1/2 [00:00<00:00,  1.56it/s]mask: torch.Size([1, 516, 161])
output shape: torch.Size([1, 516, 161])

Sanity Checking DataLoader 0: 100%|██████████| 2/2 [00:00<00:00,  2.51it/s]
                                                                           

Training: |          | 0/? [00:00<?, ?it/s]
Training:   0%|          | 0/55879 [00:00<?, ?it/s]
Epoch 0:   0%|          | 0/55879 [00:00<?, ?it/s] mask shape: torch.Size([1, 70, 224, 87, 1])
data shape: torch.Size([1, 70, 224, 87, 2])
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33mbslXL_l1kspace[0m at: [34mhttps://wandb.ai/lisha-zeng-cedars-sinai/cmr2025_task3/runs/0wpkypoo[0m
[1;34mwandb[0m: Find logs at: [1;35m../Experiments/wandb/run-20250812_185613-0wpkypoo/logs[0m
bslXL (checkpoint) training complete!
