#!/bin/bash
#SBATCH --job-name=cmr_baselineXL_task3
#SBATCH --partition=gpu
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=128G
#SBATCH --time=168:00:00
#SBATCH --gres=gpu:h100:4
#SBATCH --output=/common/lidxxlab/cmrchallenge/task3/code/Lisha/scripts-lisha/logs/my_job_%j.out
#SBATCH --error=/common/lidxxlab/cmrchallenge/task3/code/Lisha/scripts-lisha/logs/my_job_%j.err

# Working directory
WORKDIR=/common/lidxxlab/cmrchallenge/task3/code/Lisha/PromptMR-plus-Task3
cd $WORKDIR

# Initialize conda
source $(conda info --base)/etc/profile.d/conda.sh
conda activate promptmr
# conda activate cmrrecon-task3 # Lisha's environment esplhpccompbio-lv03


# Quick debug on PROCID=0 only
if [ "$SLURM_PROCID" -eq 0 ]; then
  echo "CUDA_VISIBLE_DEVICES = $CUDA_VISIBLE_DEVICES"
  nvidia-smi --query-gpu=index,name,memory.total --format=csv
  python - <<'PYCODE'
import os, torch
print("visible:", os.environ["CUDA_VISIBLE_DEVICES"])
print("CUDA available:", torch.cuda.is_available())
print("Num GPUs:", torch.cuda.device_count())
for i in range(torch.cuda.device_count()):
    p = torch.cuda.get_device_properties(i)
    print(f"  [{i}] {p.name}  •  {p.total_memory/1e9:.1f} GB")
PYCODE
  echo "Python path: $(which python)"
  echo "CUDA version (in PyTorch): $(python -c 'import torch; print(torch.version.cuda)')"
  export WANDB_API_KEY="****************************************"
  wandb login $WANDB_API_KEY
  echo "Wandb status: $(wandb status)"
  export WANDB_DEBUG=true WANDB_LOG_MODEL=true WANDB_WATCH=all
fi

# Training parameters
CONFIG_FILE="configs/train/pmr-plus/cmr25-cardiac-task3-XL.yaml"
MAX_EPOCHS=50
BATCH_SIZE=1
CKPT_PATH="/common/lidxxlab/cmrchallenge/task3/PromptMR-plus-Task3/logs/cmr2025_task3/43qr6fxh/checkpoints/best-epochepoch=13-valvalidation_loss=0.0206.ckpt"

echo "Running on node: $(hostname), SLURM_PROCID=$SLURM_PROCID"

# Launch DDP under Slurm (one task, four GPUs)
srun python main.py fit \
    --config $CONFIG_FILE \
    --trainer.accelerator=gpu \
    --trainer.strategy=ddp \
    --trainer.devices=4 \
    --trainer.max_epochs=$MAX_EPOCHS \
    --data.init_args.batch_size=$BATCH_SIZE \
    --trainer.logger.save_dir="/common/lidxxlab/cmrchallenge/task3/code/Lisha/Experiments" \
    --trainer.logger.init_args.project="cmr2025_task3" \
    --trainer.logger.init_args.tags="[baseline,promptmr_plus,comparison]" \
    --trainer.logger.init_args.name="bslXL_l1kspace" \
    --trainer.logger.init_args.log_model="all" \
    --trainer.logger.init_args.offline=false \
    --model.init_args.pretrain=True \
    --model.init_args.pretrain_weights_path=$CKPT_PATH 
    # --ckpt_path=$CKPT_PATH

echo "bslXL (checkpoint) training complete!"
